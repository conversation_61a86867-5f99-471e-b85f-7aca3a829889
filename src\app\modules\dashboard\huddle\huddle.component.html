<div class="huddleHeader">
  <h2 class="huddleTitle">Huddle</h2>
  <div class="printButtonContainer">
    <button type="button" mat-button [matMenuTriggerFor]="printMenu" class="printButton">
      <mat-icon>print</mat-icon>
    </button>
    <mat-menu #printMenu="matMenu">
      <button type="button" mat-menu-item (click)="printReport()">PDF</button>
    </mat-menu>
  </div>
</div>
<div class="wrapper">
    <div class="sidebar">
        <div class="filterWrapper">
            <epividian-huddle-filter
            (filterObjectSelected)="onFilterObjectSelected($event)"
            [huddleAllAppointments]="huddleAllAppointments"
            [siteId]="siteId"
            ></epividian-huddle-filter>
        </div>
        <div>
            <epividian-schedule-details-calendar *ngIf="safeToLoad"
                (calendarSelected)="onCalendarSelected($event)"
                [isSiteDirector]="isSiteDirector"
                [siteId]="siteId" >
            </epividian-schedule-details-calendar>
        </div>
    </div>
    <div class="details">
        <epividian-details-table *ngIf="safeToLoad"
            [selectedFilter]="selectedFilter"
            [calendarSelection]="calendarSelection"
            [siteId]="siteId"
            (huddleAppointments)="onHuddleAppointments($event)"
            (huddleAllAppointments)="onHuddleAllAppointments($event)">
        </epividian-details-table>
    </div>
</div>
