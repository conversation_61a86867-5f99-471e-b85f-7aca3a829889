import { Component, OnInit } from "@angular/core";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { environment } from "src/environments/environment";
import { DevicePinVM, DeviceType, MessageMethod, UserDevices } from "./models/USER_DEVICE_INFO";
import { UserContext } from "src/app/shared-services/user-context/user-context.service";
import { ApiRoutes, ApiTypes } from "src/app/shared-services/ep-api-handler/api-option-enums";
import { DialogResponseComponent } from "../dialog-response/dialog-response.component";
import { LayoutService } from "../../services/layout/layout.service";
import { Observable, forkJoin } from "rxjs";
import { HttpResponse } from "@angular/common/http";

@Component({
  selector: 'mobile-device-mamagement',
  templateUrl: './MobileDeviceManagement.component.html',
  styleUrls: ['./MobileDeviceManagementcomponent.scss']
})
export class MobileDeviceManagementComponent implements OnInit {
  submitStyle = "CMbtn"
  submitStyleDisabled = "CMbtnDisabled"
  showPinMgmt: boolean = false;
  isMessageVisible: boolean = false;
  sendtext: string = "";
  isValid: string = this.submitStyleDisabled;
  userEmail: string = "";
  pin: string = "";
  pinConfirm: string = "";
  UserDeviceInfo: UserDevices[] = [];
  environment = environment;
  openDialog: MatDialogRef<DialogResponseComponent,any> = {} as MatDialogRef<DialogResponseComponent, any>
  DeviceType = DeviceType;
  MessageMethod = MessageMethod;
  selectedDeviceType: number = DeviceType.Both;
  showEmailMessage: boolean =  false;
  showSMSMessage: boolean = false;

  constructor(public dialog: MatDialog,
    private userContext: UserContext,
    private layoutService: LayoutService) { }

  ngOnInit(): void {
    //Loads the data for the page with spinner control
    this.LoadData();
  }

  close() {
    this.dialog.closeAll();
  }

  //Gets the device list
  GetDeviceList(): Observable<UserDevices[]> {
    return this.userContext.apihandler.Get<UserDevices[]>(ApiTypes.V2, ApiRoutes.DeviceList, true);
  }

  //Gets the user pin and phone number
  GetUserPinAndPhone(): Observable<any> {
    return this.userContext.apihandler.Get(ApiTypes.V2, ApiRoutes.ManageDevicePinInfo, true);
  }

  //Loads the data for the page with spinner control
  LoadData(): void {
    // Show spinner before API calls
    this.layoutService.showSpinner();

    // Use forkJoin to wait for both calls to complete
    forkJoin([
        this.GetDeviceList(),
        this.GetUserPinAndPhone()
    ]).subscribe({
      next: ([deviceListData, pinAndPhoneData]) => {
        // Handle the response from GetDeviceList
        this.UserDeviceInfo = deviceListData;

        // Handle the response from GetUserPinAndPhone
        this.showPinMgmt = pinAndPhoneData.hasPin;
        this.sendtext = pinAndPhoneData.phone;
        this.userEmail = this.userContext.GetUserName();

        // Hide the spinner after both calls are done
        this.layoutService.hideSpinner();
      },
      error: (error) => {
        console.error("An error occurred: ", error);

        // Hide spinner on error
        this.layoutService.hideSpinner();
      }
    });
}

  RevokeDeviceList(deviceRow: UserDevices) {
    this.layoutService.showSpinner();
    let updateDeviceStatus = ApiRoutes.UpdateDeviceStatus.replace("{{DeviceId}}", deviceRow.id).replace("{{isActive}}", deviceRow.isActive ? "false" : "true");
    this.userContext.apihandler.Post(ApiTypes.V2, updateDeviceStatus, "", null, true).subscribe({
      next: (res) => {
        let isSuccess = res;
        this.LoadData();
      },
      error: (error) => {
        console.error("Error updating device status:", error);
        this.layoutService.hideSpinner();
      }
    });
  }

  DeleteDevice(deviceRow: UserDevices) {
    this.layoutService.showSpinner();
    let deleteDevice = ApiRoutes.DeleteDevice.replace("{{DeviceId}}", deviceRow.id);
    this.userContext.apihandler.Post(ApiTypes.V2, deleteDevice, "", null).subscribe({
      next: (res) => {
        let isSuccess = res;
        this.LoadData();
      },
      error: (error) => {
        console.error("Error deleting device:", error);
        this.layoutService.hideSpinner();
      }
    });
  }


  RevokeOrReActivateDevice(deviceRow: UserDevices): string {
    if (deviceRow.isActive) {
      return "Revoke";
    } else {
      return "Re-Activate";
    }
  }


  SendAppLink(messageMethod: MessageMethod) {
    this.layoutService.showSpinner();
    this.showEmailMessage = false;
    this.showSMSMessage = false;

    //prepares the url with the device type and method type and where to send the message
    let url = ApiRoutes.SendAppLink.replace("{{DeviceTypeId}}", this.selectedDeviceType.toString()).replace("{{MethodId}}", messageMethod.toString());
    if (messageMethod === MessageMethod.Email)
    {
      url += '?To=' + encodeURIComponent(this.userEmail);
    }
    else
    {
      url += '?To=' + encodeURIComponent(this.sendtext);
    }

    //sends the message
    this.userContext.apihandler.Post(ApiTypes.V2, url, "", true, true).subscribe({
      next: (res) => {
        if (res.ok == true)
        {
          this.layoutService.hideSpinner();
          if (messageMethod === MessageMethod.Email)
          {
            this.showEmailMessage = true;
          }
          else
          {
            this.showSMSMessage = true;
          }
        }
      },
      error: (error) => {
        console.error("Error sending app link:", error);
        this.layoutService.hideSpinner();
      }
    });
  }


  ClearPin() {
    this.pin = "";
    this.pinConfirm = "";
    this.isValid = this.submitStyleDisabled;
  }


  CreateDevicePinJson(isPinActive: boolean=false): string
  {
    let devicePinVM: DevicePinVM = {
      pin: null,
      isActive: isPinActive  };

    if (isPinActive) {
      devicePinVM.pin = Number(this.pin);
    }

    return JSON.stringify(devicePinVM);
  }


  DeactivatePin() {
    this.openResponseDialog({ title: "Deactivate Pin", content: "Are you sure you want to deactivate your pin? Deactivation will remove PIN authorization from all of your devices", dialogType: "YesNo"});
    this.openDialog.afterClosed().subscribe((data) => {
      if (data === "Confirm") {
        this.layoutService.showSpinner();
        this.userContext.apihandler.Post(ApiTypes.V2, ApiRoutes.ManageDevicePinInfo, this.CreateDevicePinJson(false), true, true).subscribe({
          next: (res: any) => {
            this.layoutService.hideSpinner();
            if (res.status === 200) { // success
              this.ClearPin();
              this.showPinMgmt = false;
              this.isMessageVisible = true;
            }
          },
          error: (error) => {
            console.error("Error deactivating pin:", error);
            this.layoutService.hideSpinner();
          }
        });
      }
    });
  }


  UpdatePin() {
    this.layoutService.showSpinner();
    this.userContext.apihandler.Post(ApiTypes.V2, ApiRoutes.ManageDevicePinInfo, this.CreateDevicePinJson(true), true,true).subscribe({
      next: (res) => {
        this.layoutService.hideSpinner();
        if (res.status === 200) { // success
          this.ClearPin();
          this.isMessageVisible = true;
        }
      },
      error: (error) => {
        console.error("Error updating pin:", error);
        this.layoutService.hideSpinner();
      }
    });
  }


  ValidatePin(update: boolean=false) {
    this.isValid = this.submitStyleDisabled;
    this.isMessageVisible = false;
    if (this.pin.length !== 6) return false;
    if (this.pin !== this.pinConfirm) return false;
    if (!this.isOnlyNumbers(this.pin)) return false;
    if (this.isRepeatedDigit(this.pin)) return false;
    if (this.isTwoSetsOfThreeConsecutive(this.pin)) return false;
    if (this.isConsecutive(this.pin)) return false;

    this.isValid = this.submitStyle;
    if (update)
    {
      this.UpdatePin();
    }
    return true;
  }


  isOnlyNumbers(s: string): boolean {
    return /^\d+$/.test(s);
  }


  isConsecutive(s: string): boolean {
    // Check for consecutive increasing digits
    if (s === '123456' || s === '234567' || s === '345678' || s === '456789') {
      return true;
    }

    // Check for consecutive decreasing digits
    if (s === '654321' || s === '543210' || s === '432109' || s === '321098') {
      return true;
    }

    return false;
  }


  isTwoSetsOfThreeConsecutive(s: string): boolean {
    const firstThree = s.substr(0, 3);
    const lastThree = s.substr(3, 3);

    const consecutiveSets = ['012', '123', '234', '345', '456', '567', '678', '789', '987', '876', '765', '654', '543', '432', '321', '210'];

    return consecutiveSets.includes(firstThree) && consecutiveSets.includes(lastThree);
  }


  isRepeatedDigit(s: string): boolean {
    return new Set(s.split('')).size === 1;
  }


  public openResponseDialog(data: any) {
    this.openDialog = this.dialog.open(DialogResponseComponent, {
      data: data, width: '400px', height: 'auto', disableClose: true
    });
  }
}
