import { QualityMeasure } from "../../src/app/shared-services/quality-measures/model/quality-measures-model";

function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export const mockQualityMeasures: QualityMeasure[] = [
  {
    measureId: 1,
    qualityName: "Currently on ART",
    groupName: "Empire",
    locationName: "Death Star",
    providerName: "Darth Vader",
    providerId: 0,
    trend: 0,
    measuresSatisfied: getRandomInt(100, 2100),
    measuresUnsatisfied: getRandomInt(0, 1100),
    newMeasures: 5,
    ongoingMeasures: 10,
    recentlySatisfied: 0,
    operaScore: "Leader",
    operaAverage: 40.3
  },
  {
    measureId: 2,
    qualityName: "Currently on ART",
    groupName: "Empire",
    locationName: "Death Star",
    providerName: "Grand Moff Tarkin",
    providerId: 0,
    trend: -1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 7,
    ongoingMeasures: 100,
    recentlySatisfied: 10,
    operaScore: "Median",
    operaAverage: 20
  },
  {
    measureId: 3,
    qualityName: "Vaccines",
    groupName: "Rebellion",
    locationName: "Hoth",
    providerName: "R2-D2",
    providerId: 1,
    trend: 1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 1,
    ongoingMeasures: 30,
    recentlySatisfied: 40,
    operaScore: "Median",
    operaAverage: 50
  },
  {
    measureId: 4,
    qualityName: "2024 - MIPS Measures",
    groupName: "Rebellion",
    locationName: "Hoth",
    providerName: "C3PO",
    providerId: 1,
    trend: 1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 5,
    ongoingMeasures: 75,
    recentlySatisfied: 15,
    operaScore: "Leader",
    operaAverage: 75
  },
  {
    measureId: 5,
    qualityName: "Vaccines",
    groupName: "Rebellion",
    locationName: "Tatooine",
    providerName: "Han Solo",
    providerId: 2,
    trend: -1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 22,
    ongoingMeasures: 70,
    recentlySatisfied: 20,
    operaScore: "-25%",
    operaAverage: 99
  },
  {
    measureId: 1,
    qualityName: "Currently on ART",
    groupName: "Empire",
    locationName: "Death Star",
    providerName: "General Veers",
    providerId: 0,
    trend: 0,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 5,
    ongoingMeasures: 10,
    recentlySatisfied: 0,
    operaScore: "25%",
    operaAverage: 40.3
  },
  {
    measureId: 2,
    qualityName: "Currently on ART",
    groupName: "Empire",
    locationName: "Death Star",
    providerName: "Director Krennic",
    providerId: 0,
    trend: 1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 7,
    ongoingMeasures: 100,
    recentlySatisfied: 10,
    operaScore: "25%",
    operaAverage: 20
  },
  {
    measureId: 3,
    qualityName: "Vaccines",
    groupName: "Rebellion",
    locationName: "Hoth",
    providerName: "Luke Skywalker",
    providerId: 1,
    trend: 0,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 1,
    ongoingMeasures: 30,
    recentlySatisfied: 40,
    operaScore: "Leader",
    operaAverage: 50
  },
  {
    measureId: 4,
    qualityName: "2024 - MIPS Measures",
    groupName: "Rebellion",
    locationName: "Hoth",
    providerName: "Leia Organa",
    providerId: 1,
    trend: -1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 5,
    ongoingMeasures: 75,
    recentlySatisfied: 15,
    operaScore: "Leader",
    operaAverage: 75
  },
  {
    measureId: 5,
    qualityName: "Vaccines",
    groupName: "Rebellion",
    locationName: "Tatooine",
    providerName: "Chewbacca",
    providerId: 2,
    trend: -1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 22,
    ongoingMeasures: 70,
    recentlySatisfied: 20,
    operaScore: "-25%",
    operaAverage: 99
  },
  {
    measureId: 1,
    qualityName: "Currently on ART",
    groupName: "Rebellion",
    locationName: "Hoth",
    providerName: "Luke Skywalker",
    providerId: 0,
    trend: 1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 5,
    ongoingMeasures: 10,
    recentlySatisfied: 0,
    operaScore: "Leader",
    operaAverage: 40.3
  },
  {
    measureId: 2,
    qualityName: "Currently on ART",
    groupName: "Rebellion",
    locationName: "Coruscant",
    providerName: "Leia Organa",
    providerId: 0,
    trend: 1,
    measuresSatisfied: getRandomInt(0, 100),
    measuresUnsatisfied: getRandomInt(0, 100),
    newMeasures: 7,
    ongoingMeasures: 100,
    recentlySatisfied: 10,
    operaScore: "Leader",
    operaAverage: 20
  },
];
