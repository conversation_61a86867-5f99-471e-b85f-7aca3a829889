<div class="workflow-details-container">
  <div class="loading-indicator" *ngIf="loadingRules">
    <mat-spinner diameter="30"></mat-spinner>
    <p>Loading workflow rules...</p>
  </div>

  <div class="workflow-details-content" *ngIf="!loadingRules">
    <div class="workflow-rules-container">
      <div class="assigned-rules">
        <h3>Assigned Rules</h3>
        <p class="help-text">Drag and drop rules to change their execution order. Rules are executed from top to bottom.</p>

        <div cdkDropList class="rule-list" (cdkDropListDropped)="dropRule($event)">
          <div class="rule-box" *ngFor="let rule of workflowRules; let i = index" cdkDrag>
            <div class="rule-content">
              <div class="rule-order">{{i + 1}}</div>
              <div class="rule-info">
                <span class="rule-name">{{rule.name}}</span>
                <span class="rule-type">{{rule.type}}</span>

                <!-- Dependencies display -->
                <div class="rule-dependencies" *ngIf="getDependenciesForRule(rule.ruleID).length > 0">
                  <span class="dependencies-label">Depends on:</span>
                  <span class="dependency-item" *ngFor="let depId of getDependenciesForRule(rule.ruleID)">
                    {{getRuleNameById(depId)}}
                  </span>
                </div>
              </div>

              <div class="rule-actions">
                <button type="button" mat-icon-button color="primary" (click)="editDependencies(rule)" matTooltip="Edit Dependencies">
                  <mat-icon>link</mat-icon>
                </button>
                <button type="button" mat-icon-button color="warn" (click)="removeRuleFromWorkflow(rule)" matTooltip="Remove Rule">
                  <mat-icon>remove_circle</mat-icon>
                </button>
              </div>
            </div>
          </div>

          <div *ngIf="workflowRules.length === 0" class="empty-list">
            No rules assigned to this workflow
          </div>
        </div>
      </div>

      <div class="available-rules">
        <h3>Available Rules</h3>
        <div class="search-container">
          <mat-form-field appearance="fill" class="search-field">
            <mat-label>Search Rules</mat-label>
            <input matInput [(ngModel)]="ruleSearchText" (keyup)="filterAvailableRules()" placeholder="Search by name or type">
            <button *ngIf="ruleSearchText" matSuffix mat-icon-button aria-label="Clear" (click)="ruleSearchText=''; filterAvailableRules()">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>

        <div class="rule-list">
          <div class="rule-box" *ngFor="let rule of filteredAvailableRules">
            <div class="rule-content">
              <div class="rule-info">
                <span class="rule-name">{{rule.name}}</span>
                <span class="rule-type">{{rule.type}}</span>
              </div>
              <button type="button" mat-icon-button color="primary" (click)="addRuleToWorkflow(rule)" matTooltip="Add Rule">
                <mat-icon>add_circle</mat-icon>
              </button>
            </div>
          </div>

          <div *ngIf="filteredAvailableRules.length === 0" class="empty-list">
            No available rules found
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Template for dependency editor dialog -->
<ng-template #dependencyDialog>
  <h2 mat-dialog-title>Edit Rule Dependencies</h2>
  <mat-dialog-content>
    <div class="dependency-editor" *ngIf="selectedRule">
      <p>Select rules that must complete before "{{selectedRule.name}}" can execute:</p>

      <form [formGroup]="dependencyForm">
        <!-- Search filter for dependencies -->
        <div class="dependency-search">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Search Dependencies</mat-label>
            <input matInput [(ngModel)]="dependencySearchText" [ngModelOptions]="{standalone: true}"
                  placeholder="Filter by rule name" (keyup)="filterDependencies()">
            <button type="button" *ngIf="dependencySearchText" matSuffix mat-icon-button aria-label="Clear"
                  (click)="dependencySearchText=''; filterDependencies()">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>

        <div class="dependency-list">
          <div class="dependency-item" *ngFor="let rule of filteredWorkflowRules">
            <mat-checkbox
              *ngIf="canBeADependency(selectedRule.ruleID, rule.ruleID)"
              [checked]="isDependencySelected(rule.ruleID)"
              (change)="toggleDependency(rule.ruleID, $event.checked)">
              {{rule.name}}
            </mat-checkbox>
            <div class="disabled-dependency" *ngIf="!canBeADependency(selectedRule.ruleID, rule.ruleID)">
              <span class="disabled-rule-name">{{rule.name}}</span>
              <span class="disabled-reason">
                {{rule.ruleID === selectedRule.ruleID ? '(This rule)' : '(Would create circular dependency)'}}
              </span>
            </div>
          </div>

          <div *ngIf="filteredWorkflowRules.length === 0" class="empty-dependencies">
            {{workflowRules.length <= 1 ? 'No other rules available to set as dependencies' : 'No rules match your search'}}
          </div>
        </div>
      </form>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button type="button" mat-button mat-dialog-close>Cancel</button>
    <button type="button" mat-raised-button color="accent" (click)="applyDependencyChanges()">Apply</button>
    <button type="button" mat-raised-button color="primary" (click)="saveDependencies()">Save Now</button>
  </mat-dialog-actions>
</ng-template>
