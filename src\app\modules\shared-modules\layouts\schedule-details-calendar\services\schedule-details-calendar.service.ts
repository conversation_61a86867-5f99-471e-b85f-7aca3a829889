import { Injectable } from '@angular/core';
import { IReportList } from 'src/app/shared-services/ep-api-handler/models/report-list.model'
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { IMenuSections } from 'src/app/modules/shared-modules/layouts/models/menu-item.model';
import { BehaviorSubject, map, Observable, of, ReplaySubject, Subject } from 'rxjs';
import { HttpParams, HttpResponseBase } from '@angular/common/http';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import {CommonModule, DatePipe } from '@angular/common';
import { IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';

@Injectable({
  providedIn: 'root'
})

export class ScheduleDetailsCalendarService {
    datePipe: DatePipe = new DatePipe('en-US');

    constructor(private apiHandler: ApiHandler, private userContext: UserContext) {}

    public getLocations(siteId: string, date: Date){

        const dateString = this.datePipe.transform(date, 'MM-dd-yyyy') ?? "";

        let url = ApiRoutes.HuddleGetLocations.toString().replace("{{siteId}}", siteId)
                                                        .replace("{{date}}", dateString);

        return this.userContext.apihandler.Get(ApiTypes.V2, url);
    }

    public getSearchLocation(siteId: string, searchStr: string): Observable<IReportParamData>{

        let url = ApiRoutes.huddleGetSearchLocations.toString().replace("{{siteId}}", siteId)
                                                        .replace("{{searchStr}}", searchStr);
                                                        
        return this.userContext.apihandler.Get(ApiTypes.V2, url);
    }

    public getProviders(siteId: string, date: Date){

        const dateString = this.datePipe.transform(date, 'MM-dd-yyyy') ?? "";

        let url = ApiRoutes.HuddleGetProviders.toString().replace("{{siteId}}", siteId)
                                                         .replace("{{date}}", dateString);

        return this.userContext.apihandler.Get(ApiTypes.V2, url);
    }
}