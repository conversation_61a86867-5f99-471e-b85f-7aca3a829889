// quality-measures.service.ts
import { Injectable } from '@angular/core';
import { mockQualityMeasures } from '../../mockapi/data/mockQualityMeasures';
import { QualityMeasure } from './quality-measures/model/quality-measures-model';
import { Observable, of } from 'rxjs'; // Import Observable and of

@Injectable({
  providedIn: 'root' // Makes the service available throughout your app
})
export class QualityMeasuresService {

  constructor() { }

  getMeasures(): Observable<QualityMeasure[]> {
    return of(mockQualityMeasures); // Use 'of' to convert the array into an Observable
  }
}
