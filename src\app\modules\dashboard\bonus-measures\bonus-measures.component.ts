import { Component, OnInit, AfterViewInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { jsPDF } from 'jspdf';
import { applyPlugin } from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { Chart, PieController, ArcElement, Tooltip, Legend } from 'chart.js';
import { BonusMeasuresService } from './services/bonus-measures.service';
import { BonusMeasure } from 'src/app/shared-services/bonus-measures/model/bonus-measures-model';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { IReportList } from 'src/app/shared-services/ep-api-handler/models';

// Apply jsPDF plugin
applyPlugin(jsPDF);

// Register Chart.js components
Chart.register(<PERSON><PERSON><PERSON><PERSON><PERSON>, ArcE<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>);

@Component({
  selector: 'app-bonus-measures',
  templateUrl: './bonus-measures.component.html',
  styleUrl: './bonus-measures.component.scss'
})
export class BonusMeasuresComponent implements OnInit, AfterViewInit, OnDestroy {
  bonusMeasures: BonusMeasure[] = [];
  table!: Tabulator;
  private resizeObserver!: any;
  columnWidths: number[] = []; // External variable to store column widths
  groupByFields: number[] = []; // groupBy fields
  toggleGrouping: boolean = false; // Flag to track grouping state
  showReportDetails = false;
  selectedRowData: any = null;
  siteId: string = '';
  public routerState: any;
  
  // Panel state management - mutually exclusive panels
  public reportNavCollapsed: boolean = false;
  public filtersCollapsed: boolean = true;
  public currentSelectedReport: string = "";


  constructor(
    private bonusMeasuresService: BonusMeasuresService,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private userContext: UserContext,
    private route: ActivatedRoute
  ) {

// Extract siteId from route parameters (following EHI_Export pattern)
    let tmpSite = this.route.snapshot.paramMap.get('siteId');
    if (tmpSite) {
      this.siteId = tmpSite;
      // Only set current site if not internal report (siteId 0)
      if (Number(tmpSite) !== 0) {
        this.userContext.SetCurrentSite(Number(tmpSite));
      }
    }
  }

  ngOnInit(): void {
    // Check sessionStorage for panel collapse state
    const shouldCollapse = sessionStorage.getItem('chorus_reports_panel_should_collapse') === 'true';
    const isInitialNavigation = sessionStorage.getItem('chorus_initial_reports_navigation') === 'true';

    // Apply collapse logic: collapse if flag is set AND it's not initial navigation
    if (shouldCollapse && !isInitialNavigation) {
      // User selected a specific report after initial navigation, collapse the reports panel
      this.reportNavCollapsed = true;
    }

    // Always clear the collapse flag after checking it
    if (shouldCollapse) {
      sessionStorage.removeItem('chorus_reports_panel_should_collapse');
    }

    // If this is not initial navigation and no collapse flag, set initial navigation flag for direct access
    if (!isInitialNavigation && !shouldCollapse) {
      sessionStorage.setItem('chorus_initial_reports_navigation', 'true');
    }

    this.loadBonusMeasures();
  }

  ngAfterViewInit(): void {
    // Initialize resize observer for responsive table
    this.initializeResizeObserver();
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.table) {
      this.table.destroy();
    }
  }

  /**
   * Load bonus measures data from the service
   */
  loadBonusMeasures(): void {
    this.spinnerService.show();

    const subscription = this.bonusMeasuresService.getBonusMeasures(this.siteId).subscribe({
      next: (data) => {
        this.bonusMeasures = data;
        this.spinnerService.hide();

        // Initialize table after data is loaded
        setTimeout(() => this.initializeTable(), 0);
      },
      error: (error) => {
        console.error('Error loading bonus measures data:', error);
        this.spinnerService.hide();
      }
    });
  }

  /**
   * Initialize the resize observer for responsive table behavior
   */
  initializeResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.table) {
          this.table.redraw();
        }
      });

      const tableContainer = document.querySelector('#bonus-measures-table');
      if (tableContainer) {
        this.resizeObserver.observe(tableContainer);
      }
    }
  }



  /**
   * Initialize the Tabulator table with bonus measures data
   */
  initializeTable(): void {
    this.table = new Tabulator("#bonus-measures-table", {
      renderVertical:"basic", //noticed some jumping when expanding a lot of groups: https://github.com/olifolkerd/tabulator/issues/4353#issuecomment-2062679149
      layoutColumnsOnNewData: true,
      height: 700, // The grouping is making the table too small on initialization, so set the height to 700px.  However, this makes the height too small on a 4k screen.
      maxHeight: 1000,
      minHeight: 300,
      dependencies: {
        XLSX: XLSX,
        jspdf: jsPDF
      },
      downloadConfig: {
        rowGroups: false, //disable row groups in download
      },
      downloadRowRange: "all",
      layout:"fitDataStretch",
      selectableRows: false,
      data: this.bonusMeasures,
      printAsHtml: true, //enable html table printing
      printStyled: true, //copy Tabulator styling to HTML table
      pagination: true,
      paginationSize: 50,
      paginationSizeSelector: [10, 25, 50, 100],
      groupBy: ["measureNm", "locationGroupName", "location"],
      groupStartOpen: [false, false, false],
      groupToggleElement: "header", //toggle group on click anywhere in the group header
      headerSortElement: "<i class='material-icons'>arrow_drop_up</i>", //material icon for sorting
      rowHeight: 30,
      columnDefaults:{
        resizable: false,
        headerSort: false,
      },
      columnHeaderVertAlign: "middle",
      columns: [
        { title: "Measure", field: "measureNm", visible: false, download: true}, //these 3 columns are hidden in the table but included in the download
        { title: "Group Name", field: "locationGroupName", visible: false, download: true },
        { title: "Location Name", field: "location", visible: false, download: true },
        { //the dummy column
          download: false,
          field: "filter", //a dummy field to allow the .getField() method to work
          title: "<span style='padding-right: 140px;'>Measure</span>Location",
          hozAlign: "center",
          width: 400,
        },
        {
          title: "Provider",
          field: "primaryProvider",
          width: 200
        },
        {
          title: "Qualifies",
          field: "totCohortCnt",
          hozAlign: "center",
          width: 100,
          cssClass: "whiteBorder",
          formatter: (cell: any) => {
            return this.formatNumberWithCommas(cell.getValue());
          }
        },
        {
          title: "<div style='text-align: right'><div class='svg-icon satisfied-icon'></div></div>",
          titleDownload: "Measures Satisfied",
          field: "satisfiedCount",
          hozAlign: "right",
          width: 120,
          cssClass: "darkBlue",
          cellClick: (_e: any, cell: any) => {
            this.handleCellClick(cell, false); // Use normal alertLvl logic
          },
          formatter: (cell: any) => { //this is so the header doesn't get the same styles that would be applied using the cssClass property.
            cell.getElement().classList.add("clickable-cell");
            return this.formatNumberWithCommas(cell.getValue());
          }
        },
        {
          title: "Measures Satisfied",
          titleDownload: "%",
          field: "satisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          width: 120,
          cssClass: "lightBlue",
          cellClick: (_e: any, cell: any) => {
            this.handleCellClick(cell, false); // Use normal alertLvl logic
          },
          formatter: (cell: any) => {
            cell.getElement().classList.add("clickable-cell");
            return '(' + cell.getValue() + '%)';
          }
        },
        {
          title: "",
          field: "pieChart",
          download: false,
          formatter: (cell: any) => {
            const data = cell.getRow().getData();
            const canvas = document.createElement("canvas");
            canvas.width = 20;
            canvas.height = 20;

            requestAnimationFrame(() => {
              this.drawPieChart(
                canvas,
                data.cohortCnt,
                data.totCohortCnt - data.cohortCnt,
                "#99d2f8",
                "#0071bc"
              );
            });
            return canvas;
          },
          hozAlign: "center",
          minWidth: 45,
          maxWidth: 60,
        },
        {
          title: "<div style='text-align: right'><div class='svg-icon unsatisfied-icon'></div></div>",
          titleDownload: "Measures Unsatisfied",
          field: "unsatisfiedCount",
          hozAlign: "right",
          width: 120,
          cssClass: "darkBlue",
          cellClick: (_e: any, cell: any) => {
            this.handleCellClick(cell, true); // Use inverted alertLvl logic
          },
          formatter: (cell: any) => {
            cell.getElement().classList.add("clickable-cell");
            return this.formatNumberWithCommas(cell.getValue());
          }
        },
        {
          title: "Measures Unsatisfied",
          titleDownload: "%",
          field: "unsatisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          width: 120,
          cssClass: "darkBlue",
          //formatter: (cell: any) => `(${cell.getValue()}%)`,
          cellClick: (_e: any, cell: any) => {
            this.handleCellClick(cell, true); // Use inverted alertLvl logic
          },
          formatter: (cell: any) => {
            cell.getElement().classList.add("clickable-cell");
            return '(' + cell.getValue() + '%)';
          }
        }
      ]
    });

    // Subscribe to the tableBuilt event
    this.table.on("tableBuilt", () => {
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
      this.groupByFields = this.table.options.groupBy;

      // Set the group headers after the table is built
      this.table.setGroupHeader((value: any, _count: any, data: any[], group: any) => {
        // Calculate the summation of the columns
        const totalCohort = data.reduce((sum: number, row: any) => sum + row.totCohortCnt, 0);
        const measuresSatisfied = data.reduce((sum: number, row: any) => sum + row.cohortCnt, 0);
        const measuresUnsatisfied = data.reduce((sum: number, row: any) => sum + row.unsatisfiedCount, 0);
        const satisfiedPercentage = totalCohort > 0 ? Math.round((measuresSatisfied / totalCohort) * 100) : 0;
        const unsatisfiedPercentage = totalCohort > 0 ? Math.round((measuresUnsatisfied / totalCohort) * 100) : 0;

        // Determine which grouping level is being rendered
        const groupField = group.getField();
        const groupLevel = this.groupByFields.indexOf(groupField);
        const paddingLevel = 100; //padding on the left side of each column
        let indentationWidth: number = 32 + (groupLevel * paddingLevel);

                // Create the custom group header HTML string
        let groupHeaderHtml = `<div class="tabulator-group-toggle tabulator-row tabulator-unselectable tabulator-calcs tabulator-calcs-top tabulator-row-even" role="row">`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[3] + this.columnWidths[4] - indentationWidth}px;">${value}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[5]}px; text-align: center;">${this.formatNumberWithCommas(totalCohort)}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[6]}px; text-align: right;">${this.formatNumberWithCommas(measuresSatisfied)}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[7]}px; text-align: center;">(${satisfiedPercentage}%)</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[8]}px; text-align: center;" data-piechart data-satisfied="${measuresSatisfied}" data-unsatisfied="${measuresUnsatisfied}"></div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[9]}px; text-align: right;">${this.formatNumberWithCommas(measuresUnsatisfied)}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[10]}px; text-align: center;">(${unsatisfiedPercentage}%)</div>`;
        groupHeaderHtml += `</div>`;

        return groupHeaderHtml;
      });

      // Attach scroll synchronization logic
      const tableElement = document.querySelector("#bonus-measures-table .tabulator-tableholder");
      if (tableElement) {
        tableElement.addEventListener("scroll", (event) => {
          const scrollLeft = (event.target as HTMLElement).scrollLeft;
          this.syncGroupHeaderScroll(scrollLeft);
        });
      }
    });

    this.table.on("columnResized", () => {
      // Update custom group column widths when columns are resized
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
    });



    this.table.on("renderComplete", () => {
      this.renderGroupHeaderPieCharts();
    });
  }

  /**
   * Synchronize group header scroll with table scroll
   */
  syncGroupHeaderScroll(scrollLeft: number): void {
    // Find all group headers and synchronize their scroll position
    // with the table's scroll position
    const groupHeaders = document.querySelectorAll(".tabulator-group");
    groupHeaders.forEach((header) => {
      (header as HTMLElement).style.transform = `translateX(-${scrollLeft}px)`;
    });
  }

  /**
   * Render pie charts in group headers
   */
  renderGroupHeaderPieCharts(): void {
    // Find all elements with the data-piechart attribute
    const pieChartElements = document.querySelectorAll('[data-piechart]');
    pieChartElements.forEach((element) => {
      // Clear any existing content
      element.innerHTML = '';

      const canvas = document.createElement('canvas');
      canvas.width = 20;
      canvas.height = 20;

      const satisfied = parseInt(element.getAttribute('data-satisfied') || '0', 10);
      const unsatisfied = parseInt(element.getAttribute('data-unsatisfied') || '0', 10);

      // Draw the pie chart using Chart.js
      this.drawPieChart(canvas, satisfied, unsatisfied, "#99d2f8", "#0071bc");

      // Append the canvas to the element
      element.appendChild(canvas);
    });
  }

  /**
   * Draw a pie chart on a canvas element
   */
  drawPieChart(canvas: HTMLCanvasElement, satisfied: number, unsatisfied: number, satisfiedColor: string, unsatisfiedColor: string): void {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const total = satisfied + unsatisfied;
    if (total === 0) return;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 1;

    // Calculate angles
    const satisfiedAngle = (satisfied / total) * 2 * Math.PI;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw satisfied portion
    if (satisfied > 0) {
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, 0, satisfiedAngle);
      ctx.closePath();
      ctx.fillStyle = satisfiedColor;
      ctx.fill();
    }

    // Draw unsatisfied portion
    if (unsatisfied > 0) {
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, satisfiedAngle, 2 * Math.PI);
      ctx.closePath();
      ctx.fillStyle = unsatisfiedColor;
      ctx.fill();
    }

    // Draw border
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 0.5;
    ctx.stroke();
  }

  /**
   * Generate filename with current report name and ISO8601 date
   */
  private generateFileName(extension: string): string {
    const reportName = this.currentSelectedReport || "Bonus Quality Measures";
    const isoDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const sanitizedReportName = reportName.replace(/\W/g, '_'); //\W matches any char that isn't a word character
    return `${sanitizedReportName}_${isoDate}.${extension}`;
  }

  /**
   * Export table data to Excel
   */
  downloadExcel(): void {
    const fileName = this.generateFileName("xlsx");
    this.table.download("xlsx", fileName, {sheetName: this.currentSelectedReport});
  }

  /**
   * Export table data to CSV
   */
  downloadCSV(): void {
    const fileName = this.generateFileName("csv");
    this.table.download("csv", fileName);
  }

  /**
   * Export table data to PDF
   */
  downloadPDF(): void {
    const fileName = this.generateFileName("pdf");
    this.table.download("pdf", fileName, {
      orientation: "landscape",
      title: this.currentSelectedReport
    });
  }

  /**
   * Print the table
   */
  printTable(): void {
    this.table.print(false, true);
  }

  /**
   * Close the report details view and return to the main table
   */
  closeReportDetails(): void {
    this.showReportDetails = false;
    this.selectedRowData = null;
  }

  /**
   * Handle cell click events for specific columns
   * @param cell - The clicked cell
   * @param invertAlertLvl - Whether to invert the alertLvl logic
   */
  handleCellClick(cell: any, invertAlertLvl: boolean): void {
    const rowData = cell.getRow().getData();
    this.selectedRowData = {
      siteId: this.userContext.GetCurrentSiteValue().toString(),
      year: rowData.reportingPeriod,
      cohortId: rowData.cohortId,
      locationCd: rowData.location,
      providerCd: rowData.primaryProvider,
      rollingWeek: rowData.rollingWeek,
      alertLvl: invertAlertLvl ? rowData.invertedFlg : !rowData.invertedFlg, // Use normal or inverted logic
      guidelineDesc: invertAlertLvl ? rowData.notMeetingDesc : rowData.meetingDesc,
      measuresCd: rowData.measureCd
    };
    this.showReportDetails = true;
    console.log('Showing report details for:', this.selectedRowData);
  }

  /**
   * Calculate the total sum of all total measures in the dataset
   */
  getTotalMeasuresSum(): number {
    if (!this.bonusMeasures || this.bonusMeasures.length === 0) {
      return 0;
    }

    return this.bonusMeasures.reduce((sum, measure) => sum + measure.totCohortCnt, 0);
  }

  /**
   * Format number with commas for US format (e.g., 1,023)
   */
  private formatNumberWithCommas(value: number): string {
    return value.toLocaleString('en-US');
  }

  /**
   * Get the reporting year from the API data
   * Returns the reportingPeriod from the first record, or current year as fallback
   */
  getMeasureYear(): number {
    if (this.bonusMeasures && this.bonusMeasures.length > 0) {
      return this.bonusMeasures[0].reportingPeriod;
    }
    // Fallback to current year if no data is available
    return new Date().getFullYear();
  }

  // Panel management methods for report selection widget
  toggleReportNav(): void {
    this.reportNavCollapsed = !this.reportNavCollapsed;
  }

  // Handle current report name change from report navigation component
  onCurrentReportChanged(reportName: string): void {
    this.currentSelectedReport = reportName;
  }

  // Handle report selection event - automatically collapse the reports panel
  onReportSelected(): void {
    this.reportNavCollapsed = true;
  }

  // Get the dynamic title for the Select Report panel
  getSelectReportTitle(): string {
    if (this.reportNavCollapsed && this.currentSelectedReport) {
      return this.currentSelectedReport;
    }
    return "Select Report";
  }
}
