import { ChangeDetectorRef, Component, EventEmitter, input, Input, OnDestroy, Output, SimpleChanges } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ScheduleDetailsCalendarService } from './services/schedule-details-calendar.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ActivatedRoute } from '@angular/router';
import { IData, IParamGroup, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { ILocationGroup } from '../models/location-group.model';
import { MatCalendar, MatDatepickerModule } from '@angular/material/datepicker';
import { ILocation } from '../models/location.model';
import { AppointmentProviders } from 'src/app/modules/dashboard/models/Appointment-Info-model';
import { PROVIDER_GROUP_LABELS, Provider<PERSON>roupKey } from 'src/app/shared/constants/provider-group-labels';
import { HuddleCalendar } from '../models/huddle-calendar.model';
import { RoleTypes } from 'src/app/shared-services/user-context/models/user-security-model';
import { FormControl } from '@angular/forms';
import { debounceTime, filter, firstValueFrom, Subject, switchMap, take, takeUntil } from 'rxjs';
import { LayoutService } from '../services/layout/layout.service';

@Component({
  selector: 'epividian-schedule-details-calendar',
  templateUrl: './schedule-details-calendar.component.html',
  styleUrl: './schedule-details-calendar.component.scss'
})
export class ScheduleDetailsCalendarComponent {

  @Output() calendarSelected: EventEmitter<HuddleCalendar> = new EventEmitter<HuddleCalendar>();
  @Input() isSiteDirector: boolean = false;
  @Input() siteId: string = "";

  public selectedDropModifier: string = "Location";
  public selectedDropValue: any; //{ key: number; value: string } | null = null;
  public selectedDate: Date = new Date();
  public currentDate: Date = new Date();
  public yesterdayDate: Date = new Date(new Date().setDate(new Date().getDate() - 1));
  public tomorrowDate: Date = new Date(new Date().setDate(new Date().getDate() + 1));
  public sections: any;
  public locations: IReportParamData = {} as IReportParamData;
  public siteLocationsList: any;
  public providers: AppointmentProviders[] = [];
  public selectedLocation: ILocation = {} as ILocation;
  public selectedProvider: string = "";
  public locationSearchControl = new FormControl();
  public searchSubject = new Subject<string>();
  public searchLocations: Subject<IData[]> = new Subject<IData[]>();
  public firstClick = true;
  private destroy$ = new Subject<void>();

  constructor(
    public calendarService: ScheduleDetailsCalendarService, private layoutService: LayoutService
  ) {}

  ngOnInit(){
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.siteId != "" && this.siteId != "0"){
      this.getLocations(this.siteId, new Date());
      this.getproviders(this.siteId, new Date());
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getLocations(siteId: string, date: Date){
    if (this.isSiteDirector && this.siteId !== "") {
      this.searchSubject.pipe(
      filter(searchStr => searchStr.length >= 2),
      debounceTime(1200),  // Wait for 2 seconds of inactivity
      switchMap(searchStr => {
        return this.calendarService.getSearchLocation(this.siteId, searchStr);
      }),
      takeUntil(this.destroy$)
      ).subscribe({
        next: (res) => {
          this.searchLocations.next(res.data);
        },
        error: (error) => {
          console.error('Error loading huddle search locations:', error);
          this.layoutService.hideSpinner();
        }
      });
    }
    else {
      this.calendarService.getLocations(this.siteId, date)
      .subscribe({
        next: (res) => {
          this.locations = res;
          this.updateDropSections(this.selectedDropModifier);
        },
        error: (error) => {
          console.error('Error loading huddle locations:', error);
          this.layoutService.hideSpinner();
        }
      });
    }
  }

  getLocationGroups(locations: IReportParamData){
    if(locations.data){
      let locationgroups: any[] = [];
      locations.groups.forEach((group, i) => {
        let temp = {
          label: group.name,
          options: locations.data.filter((f) => f.groupId === i)
        };
        locationgroups.push(temp);
      });
      this.sections = locationgroups

      // Set default location
      if (!this.selectedLocation.id || !locations.data.some(loc => loc.key.toString() == this.selectedLocation.id)){
          let location = {
            id: locations.data[0].key.toString(),
            name: locations.data[0].value
          }
          this.selectedLocation = location;
          this.selectedDropValue = locations.data[0];
          this.emitSelectedValues();
      }
    }
  }

  autoComplete(searchStr: string): void {
      this.searchSubject.next(searchStr);
  }

  // clears location list if there are less than 2 characters in the input
  // or have selected a location and you click off of the search box.
  checkSearch(text: string){
    if (text.length < 2 || this.selectedLocation)
    {
      this.searchLocations.next([]);
      return;
    }
  }

  getproviders(siteId: string, date: Date){
    this.calendarService.getProviders(siteId,date)
      .subscribe({
        next: (res) => {
        this.providers = res;
        this.updateDropSections(this.selectedDropModifier);
      },error: (error) => {
        console.error('Error loading huddle providers:', error);
        this.layoutService.hideSpinner();
      }});
  }

  getProviderGroups(providers: AppointmentProviders[]){
    const groups = [...new Set(providers.map(item => item.group))];
    let provider: any[] = [];
    let providergroups: any[] = [];

    groups.forEach((group, i) => {
      let filteredData = providers.filter((f) => f.group === group);
      provider = filteredData.map(item => ({
          groupId: i,
          key: item.provideR_ID,
          value: item.fulL_NM
      }));
      let temp = {
        label: group,
        options: provider
      }

      // Apply user-friendly label mapping
      temp.label = PROVIDER_GROUP_LABELS[group as ProviderGroupKey] || group;

      providergroups.push(temp);
    });
    if (this.selectedDropModifier == "Provider"){
      if (this.selectedProvider == "" || !this.providers.some(p => p.provideR_ID.toString() === this.selectedProvider)){
        this.selectedProvider = providergroups[0].options[0].key;
        this.selectedDropValue = providergroups[0].options[0];
      }
      this.sections = providergroups;
    }
  }

  updateDropSections(modifier: string){
    if (this.siteId != "0" && this.siteId != ""){
      if(modifier == "Provider"){
        if (this.isSiteDirector){
          this.clearLocationSearch();
        }
        this.selectedDropModifier = "Provider";
        this.getProviderGroups(this.providers)
        this.selectedLocation = {} as ILocation;
        this.emitSelectedValues();
      }
      else{
        this.selectedDropModifier = "Location";
        if (this.isSiteDirector){
          this.getLocations(this.siteId, this.selectedDate)
        }
        else{
          this.getLocationGroups(this.locations);
        }
        this.selectedProvider = "";
        this.emitSelectedValues();
      }
    }
  }

  optionSelected(selectedValue: { key: number; value: string } | null){
    if (selectedValue != null){
      if(this.selectedDropModifier == "Provider"){
        this.selectedProvider = selectedValue.key.toString();
        this.emitSelectedValues();
      }
      else{
        let location = {
          id: selectedValue.key.toString(),
          name: selectedValue.value
        }
        this.selectedLocation = location;
        this.emitSelectedValues();
      }
    }
  }

  setDate(day: string, calendar: MatCalendar<Date>){
    switch (day) {
      case 'Yesterday':
        this.selectedDate = this.yesterdayDate
        break;

      case 'Today':
        this.selectedDate = this.currentDate;
        break;

      case 'Tomorrow':
        this.selectedDate = this.tomorrowDate
        break;
    }
    calendar.activeDate = this.selectedDate;
    this.emitSelectedDate();
  }

  onLocationSelected(searchedlocation: IData){
    let location = {
          id: searchedlocation.key.toString(),
          name: searchedlocation.value
        }
    this.selectedLocation = location;
    this.emitSelectedValues();
  }

  clearLocationSearch() {
    this.locationSearchControl.setValue('');       // Reset the input field
    this.searchLocations.next([]);                // Clear search results
  }

  isSameDate(date1: Date, date2: Date): boolean {
  return date1.getDate() === date2.getDate() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getFullYear() === date2.getFullYear();
  }

  displayFn(option: IData): string {
    return option ? option.value : '';
  }

  highlightText(inputElement: HTMLInputElement) {
    if (this.firstClick){
      inputElement.select();
      this.firstClick = false;
    }
  }

  // Needed for the dropdown selection so that when you refresh the options if the
  // previously selected value is still in the option list it will stay active.
  compareOptions = (a: any, b: any): boolean => {
    return a?.value === b?.value;
  };

  emitSelectedDate() {
    if(this.selectedDropModifier == "Provider"){
      this.getproviders(this.siteId, this.selectedDate);
    }
    else{
      if (this.isSiteDirector){
        this.emitSelectedValues();
      }
      else {
        this.getLocations(this.siteId, this.selectedDate);
      }
    }
  }

  emitSelectedValues() {
    const calendarSelection: HuddleCalendar = {
      date: this.selectedDate,
      location: this.selectedLocation,
      provider: this.selectedProvider
    };

    this.calendarSelected.emit(calendarSelection);
  }
}
