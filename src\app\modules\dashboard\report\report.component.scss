// Report Dashboard with Left Stacked Panels
.report-wrapper {
  display: flex;
  flex-direction: column;
  height: 95vh;
  font-family: MuseoSans-300;
  background-color: #eff1f6;
}

// Report Header
.report-header {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #d3d5da;
  background-color: transparent;
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    .section-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      //margin-top: -30px;
      padding: 0px 0px;
      font-size: 2.2rem;
      font-weight: 500;
    }

    .dynamic-panel-badge {
      display: flex;
      align-items: center;
      gap: 6px;
      background-color: #e3f2fd;
      color: #0071BC;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 0.8rem;
      font-weight: 500;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  .header-center {
    position: absolute;
    left: 61%;
    margin-top: 10px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;

    .report-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 1.2rem;
      font-weight: 400;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: flex-end;
  }
}

// Report Content Layout
.report-content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  padding-top: 8px;
  min-height: 0;
}

// Left Sidebar Container
.left-sidebar {
  width: 275px;
  display: flex;
  flex-direction: column;
  gap: 0px; // Remove gap so panels touch each other
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%; // Constrain to parent height
  min-height: 0; // Allow shrinking below content size
  overflow: hidden; // Prevent overflow from sidebar container
}

// Sidebar Panel Base Styles - Wrapper approach for dual border
.sidebar-panel {
  // Outer wrapper for black border
  border: 1px solid #A9A9A9;
  border-radius: 0; // Default: no border radius for middle panels
  padding: 0px; // Space between black border and inner panel
  margin-top: -1px; // Overlap the top border with the panel above
  transition: flex 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // Default: panel takes full available space when open
  flex: 1;
  min-height: 0; // Critical: Allow wrapper to shrink below content size
  display: flex; // Make wrapper a flex container
  flex-direction: column; // Stack content vertically

  // When collapsed, panel takes minimal space (header only)
  &.collapsed {
    flex: 0 0 auto;
  }

  // First panel: curved top corners, straight bottom corners
  &:first-child {
    margin-top: 0;
    border-radius: 16px 16px 0 0; // More rounded corners
  }

  // Last panel: straight top corners, curved bottom corners
  &:last-child {
    border-radius: 0 0 16px 16px; // More rounded corners
  }

  // If there's only one panel, keep all corners curved
  &:first-child:last-child {
    border-radius: 16px; // More rounded corners
  }

  // Inner panel with original styling
  .panel-inner {
    background-color: #fff;
    border: 8px solid #e0e0e0;
    border-radius: inherit; // Inherit border radius from wrapper
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1; // Take all available space in wrapper
    min-height: 0; // Allow flex child to shrink below content size
  }

  // Remove inner border where panels connect
  &:not(:first-child) .panel-inner {
    border-top: none; // Remove top border on panels that aren't first
  }

  &:not(:last-child) .panel-inner {
    border-bottom: none; // Remove bottom border on panels that aren't last
  }

  // Panel header now inside the inner panel
  .panel-inner .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;

    &:hover {
      background-color: #f8f9fa;
    }

    h3 {
      margin: 0;
      font-family: Museo500-Regular;
      font-size: 1.3rem;
      font-weight: 500;
      color: #0071BC;
    }

    .panel-toggle-btn {
      margin-right: -20px;
      color: #666;
      background-color: transparent !important;

      &:hover {
        background-color: transparent !important;
      }

      // Override the specific MDC classes that cause the circular hover effect
      .mat-mdc-button-persistent-ripple,
      .mdc-icon-button__ripple {
        background-color: transparent !important;
        display: none !important;
        opacity: 0 !important;
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}

// Global override for the specific MDC classes that cause the circular hover effect
::ng-deep .panel-header .panel-toggle-btn {
  .mat-mdc-button-persistent-ripple,
  .mdc-icon-button__ripple {
    background-color: transparent !important;
    display: none !important;
    opacity: 0 !important;
  }
}

.sidebar-panel {
  .panel-inner .panel-content {
    flex: 1;
    overflow: hidden;
    min-height: 0; // Allow flex child to shrink below content size

    &.hidden {
      display: none;
    }
  }
}

// Reports Panel Specific Styles
.reports-panel {
  // Reports panel takes all available space
  flex: 1;

  .panel-inner .panel-content {
    overflow-y: auto;
    min-height: 0; // Allow flex child to shrink below content size

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Filters Panel Specific Styles
.filters-panel {
  // Filters panel only takes the space it needs
  flex: 0 0 auto;

  .panel-content {
    // Allow content to determine height
    flex: 0 0 auto;
    // Remove any default padding to let widget stretch to edges
    padding: 0;
  }

  .filters-container {
    // Remove padding to let widget stretch to edges
    padding: 0;
    background-color: #f8f9fa;
    // Take full height of panel content
    height: 100%;
    // Remove fixed height - let content determine size
    //min-height: 200px; // Minimum height for usability
    max-height: 600px; // Maximum height before scrolling
    overflow-y: auto;

    // Custom scrollbar styling for container
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .filters-widget {
    background-color: white;
    // Remove border and border-radius to stretch to panel edges
    border: none;
    border-radius: 0;
    box-shadow: none;
    // Add padding inside the widget instead of container
    padding: 16px;
    width: 100%;
    height: 100%;

    // Widget stretches to fill the entire filters container
    display: flex;
    flex-direction: column;

    // Ensure the widget content doesn't overflow
    overflow: hidden;

    // Ensure panel components fit nicely within the widget
    ::ng-deep {
      .searchCriteriaDiv {
        margin: 0;
        background-color: transparent;
        // Remove border-radius since widget has no border
        border-radius: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      // Remove any conflicting backgrounds from panel components
      > * {
        background-color: transparent;
      }
    }
  }
}

// Report Viewer Area (Right)
.report-viewer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f8f9fa; // Match main background color
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  // Ensure child components take full height
  > * {
    height: 100%;
    flex: 1;
  }
}

// Report Placeholder Styles
.report-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f8f9fa;

  .placeholder-content {
    text-align: center;
    color: #666;
    max-width: 400px;
    padding: 0px 0px;

    .placeholder-icon {
      margin-bottom: 14px;

      .checklist-icon {
        display: flex;
        flex-direction: column;
        gap: 6px;
        align-items: center;

        .checklist-row {
          display: flex;
          align-items: center;
          margin-top: -10px;
           gap: 5px;


          .check-icon {
            font-size: 42px;
            width: 38px;
            height: 38px;
            color: #0071BC;
          }

          .line-icon {
            width: 25px;
            height: 5px;
            background-color: #0071BC;
            border-radius: 2px;
            margin-top: 6px;
          }
        }
      }
    }

    .placeholder-title {
      font-family: Museo500-Regular;
      font-size: 1.5rem;
      font-weight: 500;
      color: #0071BC;
      margin: 0 0 0 0;
    }

    .placeholder-subtitle {
      font-family: MuseoSans-300;
      font-size: 1rem;
      line-height: 1.5;
      color: #666;
      margin: 0;
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .left-sidebar {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .report-content-layout {
    flex-direction: column;
    gap: 4px;
    padding: 4px;
  }

  .left-sidebar {
    width: 100%;
    flex-direction: row;
    gap: 0px; // Remove gap on mobile too so panels touch

    .sidebar-panel {
      flex: 1;
      min-height: 200px;
      margin-top: 0; // Reset margin for horizontal layout
      margin-left: -1px; // Overlap left border in horizontal layout

      // Mobile horizontal layout: first panel has curved left corners, straight right corners
      &:first-child {
        margin-left: 0; // First panel should not have negative margin
        border-radius: 16px 0 0 16px; // More rounded corners for mobile
      }

      // Mobile horizontal layout: last panel has straight left corners, curved right corners
      &:last-child {
        border-radius: 0 16px 16px 0; // More rounded corners for mobile
      }

      // If there's only one panel on mobile, keep all corners curved
      &:first-child:last-child {
        border-radius: 16px; // More rounded corners for mobile
      }

      &.collapsed {
        flex: 0 0 50px;
        min-height: 50px;
      }

      &.expanded {
        flex: 2;
      }
    }
  }

  .report-viewer-area {
    border-radius: 4px;
  }

  .report-header {
    padding: 12px 16px;

    .header-left {
      .section-title {
        font-size: 1.4rem;
      }
    }

    .header-center {
      .report-title {
        font-size: 1.4rem;
      }
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .left-sidebar,
  .sidebar-panel,
  .report-viewer-area {
    transition: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .sidebar-panel {
    border: 2px solid #000;
  }

  .panel-header {
    border-bottom: 2px solid #000;
  }

  .report-viewer-area {
    border: 2px solid #000;
  }
}

// Focus and active styles for accessibility and clean appearance
.panel-toggle-btn {
  // Remove any default Material UI borders/outlines
  border: none !important;
  outline: none !important;

  // Focus state for accessibility
  &:focus {
    outline: 2px solid #0071BC;
    outline-offset: 2px;
  }

  // Remove any borders or outlines that appear after clicking
  &:active,
  &:focus:active {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
  }

  // Ensure Material UI ripple effects don't create borders
  &.mat-button-focus-overlay,
  &.mat-button-ripple {
    border: none !important;
  }
}
