import { Component, OnInit, AfterViewInit, OnDestroy, OnChanges, SimpleChanges, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { jsPDF } from 'jspdf';
import { applyPlugin } from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ReportDetailsService } from './services/report-details.service';
import { ReportDetail } from 'src/app/shared-services/bonus-measures/model/report-details-model';
import { AnnotationComponent } from '../../annotation/annotation.component';
import { AnnotationService } from '../../annotation/annotation.service';

// Apply jsPDF plugin
applyPlugin(jsPDF);

@Component({
  selector: 'app-report-details',
  templateUrl: './report-details.component.html',
  styleUrl: './report-details.component.scss'
})
export class ReportDetailsComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @Input() siteId!: string;
  @Input() year!: number;
  @Input() cohortId!: number;
  @Input() locationCd!: string;
  @Input() providerCd!: string;
  @Input() rollingWeek!: number;
  @Input() measuresCd!: string;
  @Input() alertLvl: boolean = true;
  @Input() guidelineDesc!: string;
  @Output() backClicked = new EventEmitter<void>();

  reportDetails: ReportDetail[] = [];
  table!: Tabulator;
  private resizeObserver!: any;

  // Caching and loading state management
  private cachedData: ReportDetail[] = [];
  private lastQueryParams: string = '';
  isLoading = false;
  hasData = false;

  // Annotation dialog reference
  openDialog: MatDialogRef<AnnotationComponent, any> = {} as MatDialogRef<AnnotationComponent, any>;

  constructor(
    private reportDetailsService: ReportDetailsService,
    private spinnerService: NgxSpinnerService,
    private router: Router,
    private dialog: MatDialog,
    private annotationService: AnnotationService
  ) { }

  ngOnInit(): void {
    // Don't load data here since inputs might not be set yet
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Load data when any input parameter changes and all required params are available
    if (this.siteId && this.year && this.cohortId && this.locationCd &&
        this.providerCd && this.rollingWeek !== undefined &&
        this.alertLvl !== undefined && this.measuresCd) {

      // Create a unique key for the current parameters
      const currentQueryParams = this.createQueryParamsKey();

      console.log('Input parameters changed, checking cache for:', {
        siteId: this.siteId,
        year: this.year,
        cohortId: this.cohortId,
        locationCd: this.locationCd,
        providerCd: this.providerCd,
        rollingWeek: this.rollingWeek,
        alertLvl: this.alertLvl,
        measuresCd: this.measuresCd
      });

      // Check if this is the same query as the last one (cache hit)
      if (currentQueryParams === this.lastQueryParams && this.cachedData.length > 0) {
        console.log('Cache hit! Using cached data instead of making API call');
        this.reportDetails = [...this.cachedData];
        this.hasData = true;
        this.isLoading = false;

        // Initialize table with cached data
        setTimeout(() => this.initializeTable(), 0);
      } else {
        console.log('Cache miss or new parameters, loading fresh data');
        // Different parameters or no cached data - hide current data and load new
        this.hasData = false;
        this.isLoading = true;
        this.reportDetails = [];

        // Destroy existing table to prevent showing old data
        if (this.table) {
          this.table.destroy();
        }

        this.loadReportDetails();
      }
    }
  }

  ngAfterViewInit(): void {
    // Initialize resize observer for responsive table
    this.initializeResizeObserver();
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.table) {
      this.table.destroy();
    }
  }

  /**
   * Create a unique key for the current query parameters for caching
   */
  private createQueryParamsKey(): string {
    return `${this.siteId}-${this.year}-${this.cohortId}-${this.locationCd}-${this.providerCd}-${this.rollingWeek}-${this.alertLvl}-${this.measuresCd}`;
  }

  /**
   * Load report details data from the service
   */
  loadReportDetails(): void {
    this.spinnerService.show();
    this.isLoading = true;

    const params = {
      siteId: this.siteId,
      year: this.year,
      cohortId: this.cohortId,
      locationCd: this.locationCd,
      providerCd: this.providerCd,
      rollingWeek: this.rollingWeek,
      alertLvl: this.alertLvl,
      measuresCd: this.measuresCd
    };

    this.reportDetailsService.getReportDetails(params).subscribe({
      next: (data) => {
        // Cache the data and update state
        this.reportDetails = data;
        this.cachedData = [...data];
        this.lastQueryParams = this.createQueryParamsKey();
        this.hasData = true;
        this.isLoading = false;

        this.spinnerService.hide();
        console.log(`Loaded ${data.length} report detail records and cached them`);

        // Initialize table after data is loaded
        setTimeout(() => this.initializeTable(), 0);
      },
      error: (error) => {
        console.error('Error loading report details data:', error);
        this.isLoading = false;
        this.hasData = false;
        this.spinnerService.hide();
      }
    });
  }

  /**
   * Initialize the resize observer for responsive table behavior
   */
  initializeResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.table) {
          this.table.redraw();
        }
      });

      const tableContainer = document.querySelector('#report-details-table');
      if (tableContainer) {
        this.resizeObserver.observe(tableContainer);
      }
    }
  }

  /**
   * Initialize the Tabulator table with report details data
   */
  initializeTable(): void {
    this.table = new Tabulator("#report-details-table", {
      dependencies: {
        XLSX: XLSX,
        jspdf: jsPDF
      },
      downloadConfig: {
        rowGroups: false,
      },
      layout: "fitDataStretch",
      downloadRowRange: "all",
      selectableRows: false,
      data: this.reportDetails,
      renderVertical: "basic",
      printAsHtml: true,
      printStyled: true,
      pagination: true,
      paginationSize: 10,
      paginationSizeSelector: [10, 25, 50, 100],
      minHeight: 300,
      maxHeight: "100%",
      columnDefaults:{
        resizable: false,
        headerSort: false,
      },
      columnHeaderVertAlign: "middle",
      columns: [
        {
          title: "MRN",
          field: "mrn",
          width: 100,
          cellClick: (_e: any, cell: any) => {
            const rowData = cell.getRow().getData();
            this.openPatientFlowsheet(rowData.patientId);
          },
          formatter: (cell: any) => { //this is so the header doesn't get the same styles that would be applied using the cssClass property.
            cell.getElement().classList.add("clickable-cell");
            return cell.getValue();
          },
          headerHozAlign: "center",
          hozAlign: "center"
        },
        {
          title: "Name",
          field: "patientName",
          width: 250,
          cellClick: (_e: any, cell: any) => {
            const rowData = cell.getRow().getData();
            this.openPatientFlowsheet(rowData.patientId);
          },
          formatter: (cell: any) => {
            cell.getElement().classList.add("clickable-cell");
            return cell.getValue();
          }
        },
        {
          title: "Birth Date",
          field: "formattedBirthDate",
          width: 110,
          hozAlign: "center"
        },
        {
          title: "Last Visit Date",
          field: "formattedLastVisitDate",
          width: 110,
          hozAlign: "center"
        },
        {
          title: "Recommended Action",
          field: "actionTxt",
          width: 290,
          formatter: "textarea",
        },
        {
          title: "Description",
          field: "detailTxt",
          width: 290,
          formatter: "html",
          cssClass: "no-list-style",
          accessorDownload: (value: string) => {
            return this.formatHtmlForDownload(value);
          }
        },
        {
          title: "Comments",
          field: "annotate",
          width: 120,
          headerHozAlign: "center",
          hozAlign: "center",
          download: false,
          print: false,
          cellClick: (_e: any, cell: any) => {
            const rowData = cell.getRow().getData();
            this.openAnnotationPopup(rowData);
          },
          formatter: (cell: any) => {
            cell.getElement().classList.add("darkBlue");
            cell.getElement().classList.add("clickable-cell");
            return cell.getValue();
          }
        }
      ]
    });

    // Subscribe to table events
    this.table.on("tableBuilt", () => {
      console.log("Report details table built successfully");
    });
  }

  /**
   * Export table data to Excel
   */
  exportToExcel(): void {
    this.table.download("xlsx", "report-details.xlsx", {sheetName: "Report Details"});
  }

  /**
   * Export table data to CSV
   */
  exportToCSV(): void {
    this.table.download("csv", "report-details.csv");
  }

  /**
   * Export table data to PDF
   */
  exportToPDF(): void {
    this.table.download("pdf", "report-details.pdf", {
      orientation: "landscape",
      title: "Report Details"
    });
  }

  /**
   * Print the table
   */
  printTable(): void {
    this.table.print(false, true);
  }

  /**
   * Format HTML content for download by converting ul/li to plain text
   * @param htmlContent HTML string containing ul and li elements
   * @returns Formatted plain text suitable for Excel
   */
  formatHtmlForDownload(htmlContent: string): string {
    if (!htmlContent) return '';

    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // Find all ul elements
    const ulElements = tempDiv.querySelectorAll('ul');

    let formattedText = '';

    ulElements.forEach((ul, ulIndex) => {
      if (ulIndex > 0) formattedText += '\n'; // Add spacing between multiple lists

      const liElements = ul.querySelectorAll('li');
      liElements.forEach((li, liIndex) => {
        // Add bullet point and text content
        formattedText += ` • ${li.textContent?.trim() || ''}`;
        if (liIndex < liElements.length - 1) {
          formattedText += '\n'; // Add line break between list items
        }
      });
    });

    // If no ul/li found, return plain text content
    if (formattedText === '') {
      formattedText = tempDiv.textContent?.trim() || '';
    }

    return formattedText;
  }

  /**
   * Get total number of patients
   */
  getTotalPatients(): number {
    return this.reportDetails ? this.reportDetails.length : 0;
  }

  getQueryInformation(): string {
    const patientCount = this.reportDetails ? this.reportDetails.length : 0;
    // MeasureCd<br>guideline text<br>patient count Qualifing Patients:
    return `${patientCount} Patients for ${this.year}-${this.locationCd}-${this.providerCd}-${this.measuresCd}-${this.guidelineDesc}`;
  }

  /**
   * Handle back button click - emit event to parent component
   */
  onBackClick(): void {
    this.backClicked.emit();
  }

  getMeasuresCd(): string {
    return this.measuresCd;
  }

  getGuidelineDesc(): string {
    return this.guidelineDesc;
  }

  getPatientCountAndFilters(): string {
    const patientCount = this.reportDetails ? this.reportDetails.length : 0;
    return `${patientCount} Qualifying Patients: ${this.year} Year, ${this.locationCd} Location, ${this.providerCd} Provider`;
  }


  /**
   * Navigate to PatientFlowsheet Bold Report with Demographics_ID parameter
   */
  openPatientFlowsheet(patientId: number): void {
    console.log('Opening PatientFlowsheet for patient ID:', patientId);
    this.router.navigate(['/Dashboard/Report/' + this.siteId + '/PatientFlowsheet'], {
      queryParams: { DEMOGRAPHICS_ID: '[' + patientId + ']' }
    });
  }

  /**
   * Open annotation popup for the selected patient measure
   */
  openAnnotationPopup(rowData: ReportDetail): void {
    console.log('Opening annotation popup for patient:', rowData.patientId);

    // Construct the annotation URL following the same pattern as Bold Reports
    const visitDate = rowData.measureDt ? rowData.measureDt : '0';
    const intervalDesc = rowData.intervalDesc ? rowData.intervalDesc : 'None';

    //no need to keep the ../_layouts/15/Chorus/Member/Anotation.aspx? like it has in the SSRS report
    const annotationUrl = `DemographicId=${rowData.patientId}&ProviderId=${rowData.providerId}&MeasureId=${rowData.measureId}&ReportingYear=${this.year}&MeasureAnnotateId=${rowData.measureAnnotateId}&VisitDate=${visitDate}&IntervalDesc=${intervalDesc}`;

    // Set the annotation URL in the service and open the dialog
    this.annotationService.annotationUrl = annotationUrl;
    this.openDialog = this.dialog.open(AnnotationComponent);
  }
}
