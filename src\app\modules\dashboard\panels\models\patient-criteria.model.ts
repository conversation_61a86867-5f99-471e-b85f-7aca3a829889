//These are all the Greater Than and less Than reactive options must set default selection with All for these since no code is provided
export const ReactiveFields = ['CustomQueryDDLPatientAge','CustomQueryDDLLastHIVViralLoad','CustomQueryDDLCDCount','CustomQueryDDLNadirCD4Count']

export const ExtendedFields = ["AgeFields1","AgeFields2","ViralLoadFields1","ViralLoadFields2","CD4Fields1","CD4Fields2","NadirCD4Fields1","NadirCD4Fields2"];

export const Age = [
    'All',
    'Between',
    'Less Than',
    'Greater Than'
]

export const HCVStatus = [
    {value: "-1", text:'All'},
    {value: "1", text: 'Positive'},
    {value: "0", text: 'Negative'},
    {value: "2", text: 'Negative, Cured'}
]

export const HIVStatus = [
  {value: "-1", text:'All'},
  {value: "1", text: 'Positive'},
  {value: "0", text: 'Negative'}
]

export const LastHIVViralLoad = [
    'All',
    'Undetectable',
    'Between',
    'Less Than',
    'Greater Than'
]

export const LastCD4Count = [
    'All',
    'Between',
    'Less Than',
    'Greater Than'
]

export const NadirCD4Count = [
    'All',
    'Between',
    'Less Than',
    'Greater Than'
]

// The stored procedure takes months in and therefore the number of months need to be passed here instead of an ID value.
export const LastActiveVisit = [
  {value: "-1", text:'All'},
  {value: "6", text:'Within last 6 months'},
  {value: "12", text:'Within last 12 months'},
  {value: "18", text:'Within last 18 months'},
  {value: "24", text:'Within last 24 months'}
]

export const ARTMedication = [
  {value: "-1", text:'Any'},
  {value: "1", text:'On ART'},
  {value: "2", text:'Experienced to ART'},
  {value: "3", text:'Never on ART'}
]

export const HCVMedication = [
  {value: "-1", text:'Any'},
  {value: "1", text:'On HCV Medications'},
  {value: "2", text:'Experienced to HCV Medications'},
  {value: "3", text:'Never on HCV Medications'}
]

export class PanelData {
     public reportPath:string='';
     public searchData = {};
}

export interface CodeText
{
  value: number;
  text: string;
}


