import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, Injectable, OnD<PERSON>roy, ElementRef, Output, EventEmitter } from '@angular/core';
import { MatMenu } from '@angular/material/menu';
import { Router, NavigationEnd } from '@angular/router';
import { BoldReportComponents } from '@boldreports/angular-reporting-components';
import { IMenuSections } from 'src/app/modules/shared-modules/layouts/models/menu-item.model';
import { LayoutService } from 'src/app/modules/shared-modules/layouts/services/layout/layout.service'
import { IReportList } from 'src/app/shared-services/ep-api-handler/models';
import { last, filter, Subscription } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
@Component({
  selector: 'epividian-reportNav',
  templateUrl:'./report-nav.component.html',
  styleUrls: ['./report-nav.component.scss']
})

export class ReportNavComponent implements OnInit, On<PERSON><PERSON>roy {
  public menuSections: IMenuSections[] = [];
  public currentReportSection: string = "";
  public currentReport: string = "";
  private reportPath: string = "";
  private routerSubscription: Subscription = new Subscription();

  @Output() currentReportChanged = new EventEmitter<string>();
  @Output() reportSelected = new EventEmitter<void>();

  constructor(public layoutService: LayoutService, private router: Router, public elem: ElementRef) {}

  ngOnInit() {
  }

  ngAfterViewInit(){
    this.layoutService.loadNavMenu();
    this.layoutService.menuSections.subscribe ( (sections) => {
      this.menuSections = sections;
      this.GetCurrentReport();
    });

    // Subscribe to router events to update menu highlighting when route changes
    this.routerSubscription.add(
      this.router.events.pipe(
        filter(event => event instanceof NavigationEnd)
      ).subscribe(() => {
        this.GetCurrentReport();
      })
    );
  }

  ngOnDestroy() {
    this.routerSubscription.unsubscribe();
  }

  NavSelect(path: IReportList)
  {

    //This is needed to force the boldreport component to reload completly otherwise the report does not get re-initialized.
    let reportviewerObj = this.elem.nativeElement.querySelector("bold-reportviewer");
    if (reportviewerObj!==null)
    {
      reportviewerObj.destroy;
    }

    if (path.reportFileName.includes("CustomQuery/Criteria"))
    {
      this.reportPath = `/Dashboard/Report/${path.siteId}/${path.reportFileName.replace(".rdl","")}`
    }
    else {
      if (path.reportFileName.toLowerCase().includes(".rdl"))
      {
        this.reportPath = `/Dashboard/ReportRedirect/${path.siteId}/${path.reportFileName.replace(".rdl","")}`
      }
      else
      {
        this.reportPath = `/Dashboard/${path.reportFileName}/${path.siteId}`
      }

      //this.reportPath = `/Report/${path.siteId}/${path.reportFileName.replace(".rdl","")}`
    }

    // Always set collapse flag for report selections
    // The component will decide whether to apply it based on initial navigation state
    sessionStorage.setItem('chorus_reports_panel_should_collapse', 'true');

    // Clear the initial navigation flag since user is now actively selecting reports
    sessionStorage.removeItem('chorus_initial_reports_navigation');

    // Emit event to notify parent component that a report was selected BEFORE navigation
    this.reportSelected.emit();

    this.redirectTo(this.reportPath,path);
  }



  GetCurrentReport(){
    // Extract the path without query parameters
    const urlPath = this.router.url.split('?')[0];
    const pathSegments = urlPath.split('/');
    const reportSegment = pathSegments[4];

    this.menuSections.forEach( section => {
      section.reportsForSection.forEach( report => {
        if(reportSegment == 'CustomQuery'){
          this.currentReportSection = "Custom Queries and Reports";
          this.currentReport = "Custom Query";
        }
        if(this.router.url.split('/')[2] === 'QualityMeasures'){
          this.currentReportSection = "HIV Reports";
          this.currentReport = "Quality Measures - Angular";
        }
        if(this.router.url.split('/')[4] == report.reportFileName.replace(".rdl","") || this.router.url.split('/')[2] === 'BonusMeasures'){
          this.currentReportSection = report.categoryNm;
          this.currentReport =  report.reportName;
        }
      })
    });

    // Emit the current report name to the parent component
    this.currentReportChanged.emit(this.currentReport);
  }

  redirectTo(uri:string, path: IReportList){
    this.router.navigateByUrl(uri, {
      replaceUrl: true,
      onSameUrlNavigation: 'reload',
      state: {
        reportInfo: JSON.stringify(path)
      }
    });
  }
}
