// Quality Measures Dashboard with Left Stacked Panels
.report-wrapper {
  display: flex;
  flex-direction: column;
  height: 96vh;
  font-family: MuseoSans-300;
  background-color: #eff1f6;
  margin-top: 0px;
}

// Report Header
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 0px;
  background-color: transparent;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;
  position: relative;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    .section-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 2.2rem;
      font-weight: 500;
    }
  }

  .header-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;

    .report-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 1.75rem;
      font-weight: 500;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

// Report Content Layout
.report-content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  padding: 8px;
}

// Left Sidebar Container
.left-sidebar {
  width: 275px;
  display: flex;
  flex-direction: column;
  gap: 0px; // Remove gap so panels touch each other
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0; // Don't shrink the sidebar
  min-height: 0; // Allow shrinking below content size
  overflow: hidden; // Prevent overflow from sidebar container
}

// Sidebar Panel Base Styles - Wrapper approach for dual border
.sidebar-panel {
  // Outer wrapper for gray border
  border: 1px solid #A9A9A9;
  border-radius: 0; // Default: no border radius for middle panels
  padding: 0px; // Space between border and inner panel
  margin-top: -1px; // Overlap the top border with the panel above
  transition: flex 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // Default: panel takes full available space when open
  flex: 1;
  min-height: 0; // Critical: Allow wrapper to shrink below content size
  display: flex; // Make wrapper a flex container
  flex-direction: column; // Stack content vertically

  // When collapsed, panel takes minimal space (header only)
  &.collapsed {
    flex: 0 0 auto;
  }

  // First panel: curved top corners, straight bottom corners
  &:first-child {
    margin-top: 0;
    border-radius: 16px 16px 0 0; // More rounded corners
  }

  // Last panel: straight top corners, curved bottom corners
  &:last-child {
    border-radius: 0 0 16px 16px; // More rounded corners
  }

  // If there's only one panel, keep all corners curved
  &:first-child:last-child {
    border-radius: 16px; // More rounded corners
  }

  // Inner panel with original styling
  .panel-inner {
    background-color: #fff;
    border: 8px solid #e0e0e0;
    border-radius: inherit; // Inherit border radius from wrapper
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1; // Take all available space in wrapper
    min-height: 0; // Allow flex child to shrink below content size
  }

  .panel-inner .panel-content {
    flex: 1;
    overflow: hidden;
    min-height: 0; // Allow flex child to shrink below content size

    &.hidden {
      display: none;
    }
  }

  // Remove inner border where panels connect
  &:not(:first-child) .panel-inner {
    border-top: none; // Remove top border on panels that aren't first
  }

  &:not(:last-child) .panel-inner {
    border-bottom: none; // Remove bottom border on panels that aren't last
  }

  // Panel header now inside the inner panel
  .panel-inner .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;

    &:hover {
      background-color: #f8f9fa;
    }

    h3 {
      margin: 0;
      font-family: Museo500-Regular;
      font-size: 1.3rem;
      font-weight: 500;
      color: #0071BC;
    }

    .panel-toggle-btn {
      margin-right: -20px;
      color: #666;
      background-color: transparent !important;

      &:hover {
        background-color: transparent !important;
      }

      // Override the specific MDC classes that cause the circular hover effect
      .mat-mdc-button-persistent-ripple,
      .mdc-icon-button__ripple {
        background-color: transparent !important;
        display: none !important;
        opacity: 0 !important;
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}
// Global override for the specific MDC classes that cause the circular hover effect
::ng-deep .panel-header .panel-toggle-btn {
  .mat-mdc-button-persistent-ripple,
  .mdc-icon-button__ripple {
    background-color: transparent !important;
    display: none !important;
    opacity: 0 !important;
  }
}

// Report Viewer Area (Right)
.report-viewer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff1f6; // Match main background color
}

// Bonus Measures Specific Styles
.bonus-measures-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}


.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  margin-left: 10px;
  margin-right: 10px;
  flex-shrink: 0; // Don't shrink the header

  .header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .cohort-name {
      font-size: 1.3rem;
      font-weight: 700;
      color: #0071bc;
      text-align: center;
    }

    .cohort-description {
        font-size: 1.1rem;
        color: #777777;
    }

    .qualifying-patients {
      font-size: 0.9rem;
      color: #666;
      text-align: center;
    }

    .export-dropdown {
      position: absolute;
      right: 0;
    }
  }
}

.table-container {
  flex: 1;
  overflow: hidden;
  border-radius: 12px;
  background-color: #f9f9f9;
}

// Table and icon styles
.filled-satisfied-icon {
  color: #99d2f8;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}

.filled-unsatisfied-icon {
  color: #0089e3;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}

.measures-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
}

.number {
  text-align: right;
  padding-right: 20px;
}

/* Report Details Container */
.report-details-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.details-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}
