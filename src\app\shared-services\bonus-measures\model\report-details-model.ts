export interface ReportDetail {
  cohortId: number;
  patientId: number;
  mrn: string;
  patientName: string;
  birthDt: string;
  lastVisitDt: string;
  alertLvl: number;
  providerId: number;
  actionTxt: string;
  detailTxt: string;
  measureId: number;
  invertedFlg: boolean;
  measureAnnotateId: number;
  openFlg: boolean | null;
  measureDt: string;
  intervalDesc: string | null;
  
  // Calculated fields for display
  formattedBirthDate?: string;
  formattedLastVisitDate?: string;
  formattedMeasureDate?: string;
  annotate?: string;
}
