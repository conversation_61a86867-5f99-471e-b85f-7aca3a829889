import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatAutocomplete,  MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject } from 'rxjs';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { IData, IReportParamData, blankReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelService } from '../PanelService';
import { btnColor, DiseaseManagementControlTypes , ReportPanelTypes } from '../report-panel-enums';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectChange } from '@angular/material/select';
import { LayoutService } from 'src/app/modules/shared-modules/layouts/services/layout/layout.service';

@Component({
  selector: 'pnl-disease-management',
  templateUrl: './pnl-disease-management.component.html',
  styleUrls: ['./pnl-disease-management.component.scss'],

})
export class PnlDiseaseManagementComponent implements OnInit {

  rptbtnColor: string;
  @Input() rptpanLocation: FormControl;
  @Input() rptpanProvider: FormControl;
  @Input() rptpanCondition: FormControl;
  @Input() conditionSearch: FormControl;
  @Input() providerSearch: FormControl;
  @Input() locationSearch: FormControl;

  diseaseManagementData: IReportParamData[]= [blankReportParamData,blankReportParamData,blankReportParamData];
  filteredLocations: IReportParamData[] = [] as IReportParamData[];
  optionAll={} as IData;
  lblWarningMsg:string='To run your report, please make a selection for location, provider, and condition below. You are able to select either all locations or all providers.';
  isRunButtonEnable:boolean=false;
  noSearchPan: boolean = false;
  public pageCrlTypes: any = DiseaseManagementControlTypes;

  constructor(public panelService: PanelService, private layoutService: LayoutService) {
    this.rptpanLocation = new FormControl;
    this.rptpanProvider = new FormControl;
    this.rptpanCondition = new FormControl;
    this.conditionSearch = new FormControl;
    this.locationSearch = new FormControl;
    this.providerSearch = new FormControl;
    this.rptbtnColor= btnColor.btnSecondColor;
    this.optionAll.key=-1;
    this.optionAll.value='All';
  }

  ngOnInit(): void {
    this.isRunButtonEnable=false;

    // Show spinner before API call
    this.layoutService.showSpinner();

    this.panelService.GetPanelData(ReportPanelTypes.pnlDiseaseManagement).subscribe({
      next: (s) => {
        this.diseaseManagementData=s;
        this.getDefaults();
        this.comboFilter("All", 0);
        this.comboFilter("All", 1);
        this.comboFilter("All", 2);

        // Hide spinner on success
        this.layoutService.hideSpinner();
      },
      error: (error) => {
        console.error("Error fetching disease management panel data:", error);

        // Hide spinner on error
        this.layoutService.hideSpinner();
      }
    });
  }

  readyToRun(): void{
    this.rptbtnColor= btnColor.btnPrimaryColor;
  }

  displayFn(option):any {
    if (option)
    {
      return option.value ? option.value : option.key;
    }
      return '';
  }

  getDefaults(){
    // Location
    if (this.diseaseManagementData[0].default != null){
      this.locationSearch.setValue(this.diseaseManagementData[0].data[1]);
    }
    else{
      this.locationSearch.setValue(this.diseaseManagementData[0].data[0]);
    }
    // Provider
    if (this.diseaseManagementData[1].default != null){
      this.providerSearch.setValue(this.diseaseManagementData[1].data[1]);
    }
    else{
      this.providerSearch.setValue(this.diseaseManagementData[1].data[0]);
    }
    // Condition
    this.conditionSearch.setValue(this.diseaseManagementData[2].data[0]);

    this.setButtonColor();
  }

  onSelectionChange(event, ctrlType: DiseaseManagementControlTypes): void {

    if (DiseaseManagementControlTypes.Location == ctrlType) {
      this.rptpanLocation.setValue(event.option.value.key);
    } else if (DiseaseManagementControlTypes.Provider == ctrlType) {
      this.rptpanProvider.setValue(event.option.value.key);
    }

    this.setButtonColor();
  }

  onOptionSelected(event) {
    this.rptpanCondition.setValue(event.option.value.key);
    this.setButtonColor();
  }

  public hideSearchPanel()
  {
    this.noSearchPan = true;
  }

  setButtonColor(): void
  {
    if(this.locationSearch.value.value == "All" && this.providerSearch.value.value == "All" && this.conditionSearch.value.value == "All"){
      this.rptbtnColor= btnColor.btnSecondColor;
      this.isRunButtonEnable=false;
    }
    else if (this.locationSearch.value?.key==null || this.providerSearch.value?.key==null || this.conditionSearch.value?.key==null)
    {
      this.rptbtnColor= btnColor.btnSecondColor;
      this.isRunButtonEnable=false;
    }
    else{
      this.rptbtnColor= btnColor.btnPrimaryColor;
      this.isRunButtonEnable=true;
    }

  }

  comboFilter(searchString :string, comboId : number)
  {

    this.filteredLocations = this.panelService.ComboAutoComplete(searchString, comboId, this.diseaseManagementData, false);

  }

  // Tooltip functionality for showing full values on hover
  getTooltipText(value: any): string {
    if (!value) {
      return '';
    }

    // If value is an object with a 'value' property, return that
    if (typeof value === 'object' && value.value) {
      return value.value;
    }

    // If value is a string, return it directly
    if (typeof value === 'string') {
      return value;
    }

    return '';
  }

  shouldShowTooltip(value: any): boolean {
    const tooltipText = this.getTooltipText(value);

    // Only show tooltip if there's meaningful text and it's not just "All" or empty
    return !!(tooltipText && tooltipText.length > 0 && tooltipText !== 'All' && tooltipText.trim() !== '');
  }
}
