import { Component, EventEmitter, Input, input, Output, SimpleChanges, ViewChild  } from '@angular/core';
import {MatTooltipModule} from '@angular/material/tooltip';
import { HuddleDetail } from '../models/huddle-detail.model';
import { HuddleFilterService } from './Services/huddle-filter.service';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { HuddleFilter } from '../models/huddle-filter.model';
import { LayoutService } from '../services/layout/layout.service';

@Component({
  selector: 'epividian-huddle-filter',
  templateUrl: './huddle-filter.component.html',
  styleUrl: './huddle-filter.component.scss'
})
export class HuddleFilterComponent {

  @Output() filterObjectSelected: EventEmitter<HuddleFilter> = new EventEmitter<HuddleFilter>();
  @Input() huddleAllAppointments: HuddleDetail[] = [];
  @Input() siteId: string = "";

  public selectedFilter: string = "All Appointments";
  public selectedIndex: number = 0;
  public filters: HuddleFilter[] = [];
  public allAppointments: HuddleDetail[] = []; // Store complete dataset for count calculations

  tooltipMessage = 'Additional information goes here.';
  isTooltipDisabled = true;

  constructor(public filterService: HuddleFilterService, private layoutService: LayoutService) {}

  ngOnInit(){
    if (this.siteId != "" && this.siteId != "0"){
      this.getFilters(this.siteId);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.siteId != "" && this.siteId != "0"){
      this.getFilters(this.siteId);
    
      if (changes['huddleAllAppointments']) {
        this.selectFilter(0);
      }
    }
  }

  getFilters(siteId: string){
    this.filterService.GetFilters(siteId).subscribe({
      next: (res) => {
        this.filters = res;
        // Emit the selected filter after filters are loaded to trigger data loading
        this.emitSelectedFilter();
      },
      error: (error) => {
        console.error('Error loading huddle filters:', error);
        this.layoutService.hideSpinner();
      }
    });
  }

  // Get number of unique patients with appointments that match that filter
  getAppointmentsWithFlagSet(flagPosition: number): number {
    const numberOfUniquePatients = new Set(
      this.huddleAllAppointments
        .filter(appointment => this.isFlagSet(appointment, flagPosition))
        .map(appointment => appointment.demographicsId)
      );
    return numberOfUniquePatients.size;
  }

  isFlagSet(huddleDetail: HuddleDetail, flagPosition: number): boolean {
    // Ensure that the binary string exists and is long enough to check the flag position
    if (huddleDetail.huddleFlags && huddleDetail.huddleFlags.length > flagPosition) {
      return huddleDetail.huddleFlags[flagPosition] === '1';
    }
    return false;
  }

  selectFilter(selectedIndex: number) {
    if (this.filters.length > selectedIndex){
      this.selectedIndex = selectedIndex;
      const selected = this.filters[selectedIndex];
      if (selected) {
        this.selectedFilter = selected.name;
        this.emitSelectedFilter();
      }
    }
  }

  emitSelectedFilter() {
    if (this.filters.length > this.selectedIndex) {
      this.filterObjectSelected.emit(this.filters[this.selectedIndex]);
    }
  }
}


