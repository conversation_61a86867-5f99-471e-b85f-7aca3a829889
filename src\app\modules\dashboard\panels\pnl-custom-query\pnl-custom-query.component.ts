import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ICustomQueryParamOptions, ICustomQueryParms, PatientSelectionCriteria, PatientSelectionCriteriaInitList } from '../models/custom-query-model';
import { Age, HCVStatus, HIVStatus, LastCD4Count, LastHIVViralLoad, LastActiveVisit, NadirCD4Count, ARTMedication, HCVMedication, PanelData, ReactiveFields, ExtendedFields } from '../models/patient-criteria.model';
import { PanelService } from '../PanelService';
import { btnColor } from '../report-panel-enums';
import { IPatientsRegimen, IRegimenGroup, IRegimenPanel } from './regimen-builder/regimen-builder.models';
import { AuditService } from 'src/app/shared-services/audit.service';
import { LayoutService } from 'src/app/modules/shared-modules/layouts/services/layout/layout.service';

@Component({
  selector: 'pnl-custom-query',
  templateUrl: './pnl-custom-query.component.html',
  styleUrls: ['./pnl-custom-query.component.scss']
})
export class PnlCustomQueryComponent implements OnInit {
  public firstLoad: boolean = true;
  public defaultCode: string = "-1";
  rptbtnColor: string;
  public rptpanPatientSelectionCriteria: PatientSelectionCriteria[] = [];
  public btnClearAllStyle: string = btnColor.btnPrimaryColor;
  public ageSelectOptions = Age;
  public HCVStatusSelectOptions= HCVStatus;
  public HIVStatusSelectOptions=HIVStatus;
  public LastHIVViralLoadSelectOptions = LastHIVViralLoad;
  public LastCD4CountSelectOptions = LastCD4Count;
  public LastActiveVisitSelectOptions = LastActiveVisit;
  public NadirCD4CountSelectOptions = NadirCD4Count;
  public ARTMedicationSelectOptions = ARTMedication;
  public HCVMedicationSelectOptions = HCVMedication;
  public CustQueryOptions: ICustomQueryParamOptions = {} as ICustomQueryParamOptions;
  public noSearchPan: boolean = false;
  public NumericValidator =  [Validators.required,Validators.pattern('^[0-9]+$')];
  public AgeFields1: FormControl = new FormControl('',this. NumericValidator);
  public AgeFields2: FormControl = new FormControl('',this. NumericValidator);
  public ViralLoadFields1: FormControl = new FormControl('',this. NumericValidator);
  public ViralLoadFields2: FormControl = new FormControl('',this. NumericValidator);
  public CD4Fields1: FormControl = new FormControl('',this. NumericValidator);
  public CD4Fields2: FormControl = new FormControl('',this. NumericValidator);
  public NadirCD4Fields1: FormControl = new FormControl('',this. NumericValidator);
  public NadirCD4Fields2: FormControl = new FormControl('',this. NumericValidator);
  public regimenSummary: IRegimenPanel[] = [];

  public reqIntValidationMsg = "Not a Valid Integer";

  reportPath: string='';
  patientSelectionCriteria: PatientSelectionCriteria = new PatientSelectionCriteria();

  //Definitions for additional reactive fields that start out hidden
  extraFormFields = {
    AgeFields: 0,
    ViralLoadFields: 0,
    CD4Fields: 0,
    NadirCD4Fields: 0
  };

  custFormGroup: PatientSelectionCriteriaInitList = new PatientSelectionCriteriaInitList();
  // Creating form group
  customQuerySearchForm: any;
  tempAny: any;
  groupedRegimen: any;
  tempHolderView: ICustomQueryParms = {} as ICustomQueryParms;

  constructor(
    public panelService:PanelService,
    private formBuilder: FormBuilder,
    private router: Router,
    private auditService: AuditService,
    private layoutService: LayoutService
  ) {
    this.customQuerySearchForm = this.formBuilder.group(this.custFormGroup);
    this.rptbtnColor = this.rptbtnColor= btnColor.btnSecondColor;
    let dataSaveRestore = PanelService.CustQueryFormGroup;
    //Defaults should really just be built into the data returns if thats an options but for now i put the all options directly on the page.
    //When using formgroups you MUST set default values through code or a piped async operation.
    this.clearForm();

    if (Object.keys(PanelService.CustQueryFormGroup).length > 0)
    {
      this.extraFormFields = PanelService.reactiveInput;
      setTimeout(() => {
        this.customQuerySearchForm = dataSaveRestore;

        let custQueryKeys = Object.keys(this.customQuerySearchForm.controls);

        custQueryKeys.forEach(ctrlKey => {
          if (ExtendedFields.includes(ctrlKey))
          {
            // Instead of just copying values, replace our component FormControl instances
            // with the ones from the restored form to maintain proper binding
            switch (ctrlKey) {
              case "AgeFields1":
                this.AgeFields1 = this.customQuerySearchForm.controls[ctrlKey];
                break;
              case "AgeFields2":
                this.AgeFields2 = this.customQuerySearchForm.controls[ctrlKey];
                break;
              case "ViralLoadFields1":
                this.ViralLoadFields1 = this.customQuerySearchForm.controls[ctrlKey];
                break;
              case "ViralLoadFields2":
                this.ViralLoadFields2 = this.customQuerySearchForm.controls[ctrlKey];
                break;
              case "CD4Fields1":
                this.CD4Fields1 = this.customQuerySearchForm.controls[ctrlKey];
                break;
              case "CD4Fields2":
                this.CD4Fields2 = this.customQuerySearchForm.controls[ctrlKey];
                break;
              case "NadirCD4Fields1":
                this.NadirCD4Fields1 = this.customQuerySearchForm.controls[ctrlKey];
                break;
              case "NadirCD4Fields2":
                this.NadirCD4Fields2 = this.customQuerySearchForm.controls[ctrlKey];
                break;
            }
          }
        });

        // After restoring form values, sync the dynamic field visibility with the dropdown selections
        // This ensures that when returning from cross-page navigation, the dynamic fields are properly shown
        this.syncDynamicFieldsWithDropdowns();

      }, 200);
    }

  }

  ngOnInit(): void {
    this.loadCustQueryParamOptions();

    this.regimenSummary = PanelService.createdRegimenList;

    //this.groupedRegimen = this.groupBy(this.regimenSummary, 'regimenName');
  }

  //Hides Custom Report Panel
  public hideSearchPanel() {
    this.noSearchPan = true;
  }

  // Synchronizes dynamic field visibility with dropdown selections after state restoration
  // This fixes the issue where dynamic fields don't appear when returning from cross-page navigation
  private syncDynamicFieldsWithDropdowns(): void {
    // Check Age dropdown and set AgeFields visibility
    const ageValue = this.customQuerySearchForm.get('CustomQueryDDLPatientAge')?.value;
    if (ageValue) {
      this.setExtraFieldsForDropdownValue(ageValue, 'AgeFields');
    }

    // Check Viral Load dropdown and set ViralLoadFields visibility
    const viralLoadValue = this.customQuerySearchForm.get('CustomQueryDDLLastHIVViralLoad')?.value;
    if (viralLoadValue) {
      this.setExtraFieldsForDropdownValue(viralLoadValue, 'ViralLoadFields');
    }

    // Check CD4 Count dropdown and set CD4Fields visibility
    const cd4Value = this.customQuerySearchForm.get('CustomQueryDDLCDCount')?.value;
    if (cd4Value) {
      this.setExtraFieldsForDropdownValue(cd4Value, 'CD4Fields');
    }

    // Check Nadir CD4 Count dropdown and set NadirCD4Fields visibility
    const nadirCD4Value = this.customQuerySearchForm.get('CustomQueryDDLNadirCD4Count')?.value;
    if (nadirCD4Value) {
      this.setExtraFieldsForDropdownValue(nadirCD4Value, 'NadirCD4Fields');
    }
  }

  // Helper method to set extraFormFields based on dropdown value
  // This replicates the logic from toggleExtraFields but works with string values
  private setExtraFieldsForDropdownValue(dropdownValue: string, fieldName: string): void {
    switch(dropdownValue) {
      case "Less Than":
        this.extraFormFields[fieldName] = 1;
        break;
      case "Greater Than":
        this.extraFormFields[fieldName] = 1.5;
        break;
      case "Between":
        this.extraFormFields[fieldName] = 2;
        break;
      case "Undetectable":
        this.extraFormFields[fieldName] = 0.5;
        break;
      default:
        this.extraFormFields[fieldName] = 0;
    }
  }

  //Toggles the fields for the Viral Load Selectors
  //Notes** I used decimal numbers because it allows us to group lookup UI/logic relationships by integer
  //KEY 0 & .5 are hidden but .5 represents Undetectable in params,
  //    1 & 1.5 show the first box with alternating placeholder text
  //    2 Both range boxes are available
  //Pass the relative panel property name to bind the component.
  public toggleExtraFields(selectedOption: any, hideTypeRefbyStrName: any) {

    switch(selectedOption.value)
    {
    case "Less Than":
      this.extraFormFields[hideTypeRefbyStrName] = 1
      break;

    case "Greater Than":
      this.extraFormFields[hideTypeRefbyStrName] = 1.5;
      break;

    case "Between":
      this.extraFormFields[hideTypeRefbyStrName] = 2;
      break;

    case "Undetectable":
      this.extraFormFields[hideTypeRefbyStrName] = .5;
      break;

    default:
      this.extraFormFields[hideTypeRefbyStrName] = 0;
    }

  }

  readyToRun(): void{
    this.rptbtnColor= btnColor.btnPrimaryColor;
  }

  updatePanelWithExtraFields(): void {
    // Create a copy of the current form to avoid reference issues
    // This prevents removePanelWithExtraFields() from affecting the saved state
    const formCopy = new FormGroup({});

    // Copy all existing controls from the current form
    Object.keys(this.customQuerySearchForm.controls).forEach(key => {
      formCopy.addControl(key, this.customQuerySearchForm.controls[key]);
    });

    // Add the dynamic field controls to the copy
    formCopy.addControl('AgeFields1', this.AgeFields1);
    formCopy.addControl('AgeFields2', this.AgeFields2);
    formCopy.addControl('ViralLoadFields1', this.ViralLoadFields1);
    formCopy.addControl('ViralLoadFields2', this.ViralLoadFields2);
    formCopy.addControl('CD4Fields1', this.CD4Fields1);
    formCopy.addControl('CD4Fields2', this.CD4Fields2);
    formCopy.addControl('NadirCD4Fields1', this.NadirCD4Fields1);
    formCopy.addControl('NadirCD4Fields2', this.NadirCD4Fields2);

    // Save the copy, not the original form
    PanelService.CustQueryFormGroup = formCopy;
  }

  removePanelWithExtraFields(): void {
    this.customQuerySearchForm.removeControl('AgeFields1')
    this.customQuerySearchForm.removeControl('AgeFields2');
    this.customQuerySearchForm.removeControl('ViralLoadFields1');
    this.customQuerySearchForm.removeControl('ViralLoadFields2');
    this.customQuerySearchForm.removeControl('CD4Fields1');
    this.customQuerySearchForm.removeControl('CD4Fields2');
    this.customQuerySearchForm.removeControl('NadirCD4Fields1');
    this.customQuerySearchForm.removeControl('NadirCD4Fields2');
  }

  saveAndRouteToRegimenBuilder() {
    PanelService.reactiveInput = this.extraFormFields;
    this.updatePanelWithExtraFields();
    this.router.navigate(['/Dashboard/regimenBuilder']);
  }

  //Loads all the Custom Query Dropdown selectors
  loadCustQueryParamOptions() {
    this.panelService.GetCustomQueryParamOptions().subscribe((s: ICustomQueryParamOptions) => {
      if (s)
      {
        this.CustQueryOptions = s;
        PanelService.FormControlsData = s;
      }
      this.layoutService.hideSpinner();
    });
  }

  //Rests the form to its initial state with nothing selected
  public clearForm() {

    //This is a hack to get around the fact that the form is not being reset properly.
    if (this.firstLoad)
    {
      this.firstLoad = false;
    }
    else
    {
      PanelService.createdRegimenList = [];
      this.regimenSummary = [];
      PanelService.reactiveInput = {} as any;
    }

    let formKeysList = Object.keys(this.customQuerySearchForm.controls);
    let updateCriteriaDefault = {} as FormControl;
    formKeysList.forEach(keyName => {
      updateCriteriaDefault = this.customQuerySearchForm.controls[keyName] as any;
      if (ReactiveFields.includes(keyName))
      {
          updateCriteriaDefault.setValue("All");
      }
      else
      {
          updateCriteriaDefault.setValue("-1");
      }
    });

    Object.keys(this.extraFormFields).forEach(key => {
      this.extraFormFields[key]=0;
      let field1 = key+'1';
      let field2 = key+'2';
      this[field1].value = "";
      this[field2].value = "";
    });

  }

  //Loops through all formcontrols objects and fills them in if something was selected.
  RunReport() {
    PanelService.reactiveInput = this.extraFormFields;

    // Save the form state WITH dynamic fields before removing them
    // This ensures dynamic field values are preserved for state restoration
    this.updatePanelWithExtraFields();

    // Now remove the dynamic fields for validation and processing
    this.removePanelWithExtraFields();
    let isValid:boolean = true;
    let custQueryKeys = Object.keys(this.customQuerySearchForm.controls);

    custQueryKeys.forEach(ctrlKey => {
        let selectorKeys = Object.keys(this.patientSelectionCriteria);
        let found: boolean = false
        let ct = -1;
        while (!found)
        {
          ct++;
          if (ctrlKey.includes(selectorKeys[ct]))
          {
            found = true;
          }
        }

        //Handles Reactive Error Handling definitly better to add everything to a fromgroup and use that control validation
        Object.keys(this.extraFormFields).forEach(key => {
          if (this.extraFormFields[key]>=1)
          {
            let field1 = key+'1';
            let field2 = key+'2';

            if (this[field1].value.trim() == "" || this[field1].invalid)
            {
              this[field1].value = ' ';
              isValid = false;
            }

            if ((this[field2].value.trim() == "" || this[field2].invalid) && this.extraFormFields[key]>=2)
            {
              this[field2].value = ' ';
              isValid = false;
            }

          }

        });

        //loads data from controls that have default values only
        if (this.customQuerySearchForm.controls[ctrlKey].value!='')
        {
          if (ReactiveFields.includes(ctrlKey) && this.customQuerySearchForm.controls[ctrlKey].value=="All")
          {
            this.patientSelectionCriteria[selectorKeys[ct]] = "|";
          }
          else
          {
              this.patientSelectionCriteria[selectorKeys[ct]] = this.formatFormValues(selectorKeys[ct],this.customQuerySearchForm.controls[ctrlKey].value);
          }
        }

      });

      //Loads data from the Regimen Build Panel
      let createdRegimenList = PanelService.createdRegimenList;
      let patientRegimes: IPatientsRegimen[] = [];
      let count: number = 1;
      createdRegimenList.forEach((createdRegimen: IRegimenPanel) => {
        createdRegimen.regimenList.forEach((medGroupData: IRegimenGroup) => {
          let groupCode = medGroupData.group.code;
          medGroupData.medicationName.forEach( medication => {
            let newPatientsRegimen: IPatientsRegimen = {
              id: String(count),
              levelId: createdRegimen.levelId,
              medicationGroup: this.panelService.getTextForMedGroupId(groupCode),
              status: String(medGroupData.selectedOption),
              brandName: medication.brndNM,
              option: String(createdRegimen.chainMethod),
              brandId: String(medication.brndId),
              discontinuedLookback: createdRegimen.discontinuedLookback,
              discontinuedRDBtnVal: createdRegimen.discontinuedRDBtnVal,
              currentLookBack: String(createdRegimen.currentlookBack),
            }
            patientRegimes.push(newPatientsRegimen);
            count++;
          })
        })
      });

    if (isValid)
    {
      this.rptpanPatientSelectionCriteria = [this.patientSelectionCriteria];
      let custParams: ICustomQueryParms = { PatientSelectionCriteria: this.rptpanPatientSelectionCriteria,
                                            RegimenSelection: patientRegimes } as ICustomQueryParms;
      this.tempHolderView = custParams;

      this.auditReportParameters(custParams);
      this.panelService.RunReportSubTrigger(custParams);
    }

  }

  private auditReportParameters(params: ICustomQueryParms): void {
    // Reset as we are adding new parameters
    this.auditService.resetParamAudits();

    // Process PatientSelectionCriteria
    params.PatientSelectionCriteria.forEach((criteria, index) => {
      for (const [key, value] of Object.entries(criteria)) {
        const paramName = `PatientSelectionCriteria[${index}].${key}`;
        this.auditService.addParamAudit(paramName, value, false);
      }
    });

    // Process RegimenSelection
    params.RegimenSelection.forEach((regimen, index) => {
      for (const [key, value] of Object.entries(regimen)) {
        const paramName = `RegimenSelection[${index}].${key}`;
        this.auditService.addParamAudit(paramName, value, false);
      }
    });

    // Save all audited parameters
    this.auditService.saveCurrentAudit();
  }

  //this helps format fields that support less than greater than and between..
  //Also allows pass through for regular fields.
  formatFormValues(currentField: string, currentFieldValue: string): string {
    let tmpValue = "";

    //Fields needs to be moved into a single moddle so you could create a pattern to loop through by name and have 1 case
    //based of of the currentField passed in.
    switch(currentField)
    {
      case "Age":
        tmpValue = this.parseGtLtFields(this.extraFormFields["AgeFields"],this.AgeFields1.value,this.AgeFields2.value);
        break;
      case "ViralLoad":
        tmpValue = this.parseGtLtFields(this.extraFormFields["ViralLoadFields"],this.ViralLoadFields1.value,this.ViralLoadFields2.value);
        break;
      case "CDCount":
        tmpValue = this.parseGtLtFields(this.extraFormFields["CD4Fields"],this.CD4Fields1.value,this.CD4Fields2.value);
        break;
      case "NadirCD4Count":
        tmpValue = this.parseGtLtFields(this.extraFormFields["NadirCD4Fields"],this.NadirCD4Fields1.value,this.NadirCD4Fields2.value);
        break;
      default:
        tmpValue = currentFieldValue;
    }

  return tmpValue;
  }

  //Parses the Greater Than and Less then form objects lt|gt
  parseGtLtFields(optionSelect: number, val1: string, val2: string) : string {
    let tmpValue = "";
    if (optionSelect>=2)
    {
      tmpValue = `${val2}|${val1}`;
    }
    else if (optionSelect>1)
    {
      tmpValue = `|${val1}`;
    }
    else if (optionSelect>=1)
    {
      tmpValue = `${val1}|`;
    }
    else if (optionSelect>0)
    {
      tmpValue = `1`;
    }

    return tmpValue;
  }

}
