import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiRoutes, ApiTypes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { QualityMeasure } from 'src/app/shared-services/quality-measures/model/quality-measures-model';
import { mockQualityMeasures } from '../../../../../../mockapi/data/mockQualityMeasures';

// Interface for location API response
export interface LocationApiResponse {
  value: string;
  label: string;
  group?: string;
  disabled?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class QualityMeasuresService {
  constructor(
    private apiHandler: ApiHandler,
    private userContext: UserContext
  ) {}

  /**
   * Get locations for quality measures filtering
   * @param siteId Optional site ID, if not provided will use current site from UserContext
   * @returns Observable with location data
   */
  public getLocations(siteId?: string): Observable<LocationApiResponse[]> {
    // Get siteId from parameter or use current site from UserContext
    const currentSiteId = siteId || this.userContext.GetCurrentSiteValue().toString();

    // Replace the siteId placeholder in the API route
    const apiRoute = ApiRoutes.QualityMeasuresGetLocations.replace('{siteId}', currentSiteId);

    // Make the API call
    return this.apiHandler.Get<LocationApiResponse[]>(
      ApiTypes.V2,
      apiRoute,
      false
    );
  }

  /**
   * Get quality measures
   * @returns Observable with quality measure data
   */
  public getMeasures(): Observable<QualityMeasure[]> {
    // For now, return mock data
    // In the future, this would call an API endpoint
    return of(mockQualityMeasures);
  }
}
