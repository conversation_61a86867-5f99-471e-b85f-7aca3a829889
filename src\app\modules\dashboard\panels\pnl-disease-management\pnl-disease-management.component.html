<div class="searchCriteriaDiv">
  <!-- Warning Message -->
  <div class="warning-message" *ngIf="lblWarningMsg">
    <p class="lblWarningMsg">{{lblWarningMsg}}</p>
  </div>

  <!-- Location Row -->
  <div class="field-row">
    <label class="field-label">Location:</label>
    <input #location matInput mat-form-field class="form-select form-select-sm field-input"
    [matAutocomplete]="autoLocation" name="rptpanLocation" (keyup)="comboFilter(location.value, 0)"
    [formControl]="locationSearch" placeholder="Select or search for a location"
    [matTooltip]="getTooltipText(locationSearch.value)"
    matTooltipPosition="above"
    [matTooltipDisabled]="!shouldShowTooltip(locationSearch.value)">
    <mat-autocomplete #autoLocation="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onSelectionChange($event, pageCrlTypes.Location)">
      <mat-option [value]="optionAll">{{optionAll.value}}</mat-option>
      <mat-optgroup *ngFor="let locGroup of diseaseManagementData[0].groups" [label]="locGroup.name">
        <mat-option *ngFor="let locData of panelService.getGroupData(locGroup.id, filteredLocations[0])" [value]="locData">{{locData.value}}</mat-option>
      </mat-optgroup>
    </mat-autocomplete>
  </div>

  <!-- Provider Row -->
  <div class="field-row">
    <label class="field-label">Provider:</label>
    <input matInput #provider mat-form-field class="form-select form-select-sm field-input"
    [matAutocomplete]="autoProvider" (keyup)="comboFilter(provider.value, 1)" name="rptpanProvider"
    [formControl]="providerSearch" placeholder="Select or search for a provider"
    [matTooltip]="getTooltipText(providerSearch.value)"
    matTooltipPosition="above"
    [matTooltipDisabled]="!shouldShowTooltip(providerSearch.value)">
    <mat-autocomplete #autoProvider="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onSelectionChange($event, pageCrlTypes.Provider)">
      <mat-option [value]="optionAll">{{optionAll.value}}</mat-option>
      <mat-optgroup *ngFor="let providerGroup of diseaseManagementData[1].groups" [label]="providerGroup.name">
        <mat-option *ngFor="let providerData of panelService.getGroupData(providerGroup.id, filteredLocations[1])" [value]="providerData">{{providerData.value}}</mat-option>
      </mat-optgroup>
    </mat-autocomplete>
  </div>

  <!-- Condition Row -->
  <div class="field-row">
    <label class="field-label">Condition:</label>
    <input #condition matInput mat-form-field class="form-select form-select-sm field-input"
    [matAutocomplete]="autoCondition" name="rptpanCondition" [formControl]="conditionSearch"
    (keyup)="comboFilter(condition.value, 2)" placeholder="Select or search for a condition"
    [matTooltip]="getTooltipText(conditionSearch.value)"
    matTooltipPosition="above"
    [matTooltipDisabled]="!shouldShowTooltip(conditionSearch.value)">
    <mat-autocomplete #autoCondition="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onOptionSelected($event)">
      <mat-option *ngFor="let retData of filteredLocations[2].data" [value]="retData">{{retData.value}}</mat-option>
    </mat-autocomplete>
  </div>

  <!-- Run Button Row -->
  <div class="button-row">
    <button type="button" (click)="panelService.InitBoldReport()" [disabled]="!isRunButtonEnable"
    id="reportViewer_Control_viewReportClick" aria-describedby="reportViewer_Control_viewReportClick"
    [ngClass]="rptbtnColor" class="run-button">Run</button>
  </div>
</div>
