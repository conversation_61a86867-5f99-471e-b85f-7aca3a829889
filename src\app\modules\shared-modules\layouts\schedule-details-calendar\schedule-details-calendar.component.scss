.wrapper{
    width: 275px;
    border-radius: 15px;
    padding: 10px;
    background: #EAEAEA ;
    border: 0.8px solid lightgrey;
    font-family: "Museo 500", Arial, sans-serif;
}

.title{
    font-size: 16px;
    font-family: "Museo500-Regular";
    color: #66a9d7;
    margin-bottom: 5px;
}

.topBar{
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.buttonWrapper{
    display: flex;
    justify-content: space-evenly;
    background: white;
    border-radius: 15px;
    margin: 0px 15px 10px 15px;
    padding: 2px;
}

.selectWrapper{
    display: flex;
    justify-content: center;
}

.selector{
    width: 95%;
    height: 40px;
    border-radius: 15px;
    border: lightgrey solid;
    padding-left: 5px;
    font-family: "MuseoSans-700";
    font-size: 15px;
    color: #0071BC;
}

.divider {
    margin: 10px;
}

.calendarWrapper{
    background: white;
    padding: 5px;
    border-radius: 20px;
    border: lightgrey solid;
}

.dayButtonWrapper{
    display: flex;
    justify-content: space-evenly;
    background: #EFEFEF;
    border-radius: 15px;
    padding: 2px;
}

.calendar{
    margin-top: -20px !important;
}

.calendar ::ng-deep .mat-calendar-body-selected {
    background-color: #0071BC;
    color: white;
  }

.selectDrop{
    padding: 10px;
    margin: 10px;
}

.labelSection{
    font-family: "MuseoSans-500";
    color:grey;
}

.options{
    font-family: "MuseoSans-500";
    color: #0071BC;
}

.customBtn{
    background: none !important;
    border: none !important;
    width: 100%;
    font-size: 0.8rem;
    font-family: "MuseoSans-300";
    color: #666666;
    border-radius: 15px;
}

.customBtn:hover{
    background: #80A9C8 !important;
}

.customBtn:active{
    background: #0071BC !important;
    color: white;
}

.customBtn:focus{
    background: #0071BC !important;
    color: white;
}

.active {
    background: #0071BC !important;
    color: white;
}
