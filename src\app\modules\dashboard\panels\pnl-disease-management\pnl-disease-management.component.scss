// Main container following huddle wrapper pattern
.searchCriteriaDiv {
    position: relative;
    //width: 275px;
    font-family: "Museo 500", Arial, sans-serif;
  //  margin: 2px;
    //padding: 15px;
    width: 100%;
    z-index: 1;
}

// Warning Message
.warning-message {
    margin-bottom: 5px;
    background: white;
    border-radius: 10px;
   // padding: 8px;
}

.lblWarningMsg {
    font-size: 12px;
    margin: 0;
    color: #666;
    line-height: 1.3;
    font-family: "MuseoSans-300", Arial, sans-serif;
}

// Field Rows - more compact layout
.field-row {
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.field-label {
    color: #0071BC;
    font-weight: 500;
    margin: 0;
    font-size: 13px;
    font-family: "MuseoSans-500", Arial, sans-serif;
}

// Input styling following huddle selector pattern
.field-input {
    width: 95%;
    height: 40px;
    border-radius: 4px;
    border: lightgrey solid 1px;
   // padding: 0 6px;
    font-family: "MuseoSans-700", Arial, sans-serif;
    font-size: 14px;
    background-color: white;
    box-sizing: border-box;

    // Focus and hover states
    &:focus {
        outline: none;
        border-color: #0071BC;
        box-shadow: 0 0 0 2px rgba(0, 113, 188, 0.1);
    }

    &:hover {
        border-color: #80A9C8;
    }

    // Placeholder styling
    &::placeholder {
        color: #999;
        font-family: "MuseoSans-500", Arial, sans-serif;
        font-size: 14px;
    }
}

// Run Button Row
.button-row {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}

.run-button {
    width: 95% !important;
    height: 40px !important;
    border-radius: 15px !important;
    border: none !important;
    cursor: pointer;
    font-size: 15px;
    font-family: "MuseoSans-700", Arial, sans-serif;
    color: white;
    box-sizing: border-box;

    &:hover:not(:disabled) {
        background-color: #80A9C8 !important;
    }

    &:disabled {
        background-color: #ccc !important;
        cursor: not-allowed;
    }
}

// Material UI Autocomplete Overrides
::ng-deep .mat-autocomplete-panel {
    border-radius: 15px !important;
    border: 1px solid lightgrey !important;
    font-family: "MuseoSans-500", Arial, sans-serif !important;
    max-height: 300px !important;
}

::ng-deep .mat-option {
    font-family: "MuseoSans-500", Arial, sans-serif !important;
    color: #0071BC !important;
    font-size: 15px !important;
    padding: 8px 16px !important;
    line-height: 1.4 !important;
}

::ng-deep .mat-option:hover:not(.mat-option-disabled) {
    background: rgba(0, 113, 188, 0.1) !important;
}

::ng-deep .mat-option.mat-selected:not(.mat-option-multiple) {
    background: #0071BC !important;
    color: white !important;
}

::ng-deep .mat-optgroup-label {
    font-family: "MuseoSans-500", Arial, sans-serif !important;
    color: grey !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 16px 4px 16px !important;
}

::ng-deep .mat-optgroup .mat-option {
    padding-left: 24px !important;
}

// Tooltip styling
::ng-deep .mat-tooltip {
    background-color: #333 !important;
    color: white !important;
    font-size: 12px !important;
    font-family: "MuseoSans-500", Arial, sans-serif !important;
    border-radius: 4px !important;
    padding: 6px 8px !important;
    max-width: 300px !important;
    word-wrap: break-word !important;
    line-height: 1.3 !important;
}

// Responsive Design
@media (max-width: 768px) {
    .searchCriteriaDiv {
        width: min(275px, 90vw);
        max-width: 275px;
        min-width: 250px;
    }

    .field-input {
        font-size: clamp(14px, 2.5vw, 15px);
    }

    .field-label {
        font-size: clamp(12px, 2vw, 13px);
    }
}

@media (max-width: 480px) {
    .searchCriteriaDiv {
        padding: 8px;
        min-width: 240px;
    }

    .field-row {
        margin-bottom: 8px;
        gap: 3px;
    }

    .field-input {
        height: 36px;
        padding: 0 10px;
    }

    .run-button {
        height: 36px !important;
        font-size: 14px;
    }
}

// Legacy styles for backward compatibility
.elementDiv {
    padding-right: 15px;
    display: table-cell;
    color: #0071bc;
    float: left;
    font-weight: 500;
}
