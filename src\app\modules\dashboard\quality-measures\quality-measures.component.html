<!--Quality Measures Dashboard with Left Stacked Panels-->
<div class="report-wrapper">
  <!-- Report Header -->
  <div class="report-header">
    <div class="header-left">
      <h2 class="report-title">Quality Measures</h2>
    </div>
  </div>

  <!-- Report Content Layout -->
  <div class="report-content-layout">
    <!-- Left Sidebar: Stacked Panels -->
    <div class="left-sidebar">
      <!-- Reports Panel -->
      <div class="sidebar-panel reports-panel"
           [class.collapsed]="reportNavCollapsed">
        <div class="panel-inner">
          <div class="panel-header"
               (click)="toggleReportNav()"
               [attr.aria-label]="reportNavCollapsed ? 'Show reports and hide filters' : 'Show filters and hide reports'"
               title="Toggle Reports">
            <h3>{{ getSelectReportTitle() }}</h3>
            <button
              type="button"
              mat-icon-button
              class="panel-toggle-btn"
              (click)="$event.stopPropagation(); toggleReportNav()"
              [attr.aria-label]="reportNavCollapsed ? 'Show reports and hide filters' : 'Show filters and hide reports'"
              title="Toggle Reports">
              <mat-icon>{{ reportNavCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
            </button>
          </div>
          <div class="panel-content" [class.hidden]="reportNavCollapsed">
            <epividian-reportNav
              (currentReportChanged)="onCurrentReportChanged($event)"
              (reportSelected)="onReportSelected()">
            </epividian-reportNav>
          </div>
        </div>
      </div>

      <!-- Filters Panel -->
      <div class="sidebar-panel filters-panel"
           [class.collapsed]="filtersCollapsed">
        <div class="panel-inner">
          <div class="panel-header"
               (click)="toggleFilters()"
               [attr.aria-label]="filtersCollapsed ? 'Show filters and hide reports' : 'Show reports and hide filters'"
               title="Toggle Filters">
            <h3>Quality Measure Filters</h3>
            <button
              type="button"
              mat-icon-button
              class="panel-toggle-btn"
              (click)="$event.stopPropagation(); toggleFilters()"
              [attr.aria-label]="filtersCollapsed ? 'Show filters and hide reports' : 'Show reports and hide filters'"
              title="Toggle Filters">
              <mat-icon>{{ filtersCollapsed ? 'expand_more' : 'expand_less' }}</mat-icon>
            </button>
          </div>
          <div class="panel-content" [class.hidden]="filtersCollapsed">
            <div class="filters-container">
              <div class="filters-widget">
                <app-quality-measure-filters (filtersChanged)="onFiltersChanged($event)"></app-quality-measure-filters>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right: Quality Measures Content -->
    <div class="report-viewer-area">
      <div class="quality-measures-container">
        <div class="header">
          <div class="header-content">
            <span class="qualifying-patients">{{ getTotalMeasuresSum() | number }} Qualifying Patients: COMES FROM FILTER, Measure Year FILTER</span>
            <div class="export-dropdown">
              <app-download-menu
                (printClicked)="printTable()"
                (pdfExportClicked)="downloadPDF()"
                (excelExportClicked)="downloadExcel()"
                (csvExportClicked)="downloadCSV()">
              </app-download-menu>
            </div>
          </div>
        </div>
        <div class="table-notes">
            <div *ngIf="qualityMeasures.length > 0">
              <div id="quality-measures-table"></div>
            </div>
            <div *ngIf="qualityMeasures.length === 0">
              No quality measures available.
            </div>
            <div class="data-notes">
              <h3>Data Notes</h3>

              <ul>
                <li>The Merit-based Incentive Payment System (MIPS) is a CMS quality reporting program for covered Physician Fee
                  Schedule services furnished to Medicare Part B Fee-For-Service beneficiaries. All 2022 MIPS &amp; eCQM
                  measures can be found at: <a href="https://qpp.cms.gov/">https://qpp.cms.gov/</a></li>
                <li>The All OPERA Average comparison data is only available to sites participating in OPERA</li>
                <li>For patients with a recent HIV diagnosis (last 6 months) or are new to the practice (first visit was within
                  the last 6 months), the Engaged Patient measure is adjusted to allow for only one visit in the last year.</li>
                <li>Patients who are deceased are included in these measures.</li>
                <li>PCP prophylaxis: CD4 absolute &lt;= 200 and not on Bactrim or Atovaquone or Pentamidine</li>
                <li>Toxoplasma prophylaxis: CD4 absolute &lt;= 100 and not on Bactrim or (Dapsone plus Pyrimethamine plus
                  Leucovorin) or Atovaquone</li>
                <li>Mycobacterium avium complex prophylaxis (MAC): CD4 absolute &lt;=50 and not on Azithromycin or
                  Clarithromycin or Rifabutin</li>
                <li>Custom KPIs designed and developed to meet your enterprise specific health performance targets and
                  objectives.</li>
                <li>VAX measures are based on the CDC recommended adult immunization schedule for ages 19 years or older and can
                  be found at: <a
                    href="https://www.cdc.gov/vaccines/schedules/downloads/adult/adult-combined-schedule.pdf">https://www.cdc.gov/vaccines/schedules/downloads/adult/adult-combined-schedule.pdf</a>
                </li>
              </ul>

              <p><span class="opera-score-title">OPERA &reg; Score</span> - A realtime calculation based on a linear percentage distribution of the total OPERA
              population (excluding the current site data) in relation to the data measurement being compared.</p>
              <ul>
                <li class="score-item">&darr;25% - Represents a measure that falls in the bottom 25% of the Active OPERA
                  population.</li>
                <li class="score-item">Median - Represents a measure that lies between 25% and 75% of the Active OPERA
                  population.</li>
                <li class="score-item">&uarr;25% - Represents a measure that is in the top 25% of the Active OPERA population
                </li>
                <li class="score-item">Leader - A measure that exceeds all of the current Active OPERA population.</li>
                <li>The OPERA Score comparison data is only available to sites participating in OPERA</li>
              </ul>

            </div>
        </div>
      </div>
    </div>
  </div>
</div>
