import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { catchError, Observable, of, throwError, Subject } from 'rxjs';
import {ApiTypes } from './api-option-enums'
import moment from 'moment';
import { AuditService } from '../audit.service';


@Injectable({
  providedIn: 'root',
})
export class ApiHandler {
  data: any;
  private bearerToken: Subject<string> = new Subject<string>();
  private apiToken: string = "";
  private nextTimeToCheckExpire: Date = new Date();
  //private browserInfo = `${navigator.platform}; ${navigator.userAgent}`

  constructor(private httpClient: HttpClient, private auditService: AuditService) {
    this.bearerToken.subscribe((token) => {
      this.apiToken = token;
    });
  }

  /**
   * Set the bearer token for the API handler.
   * @param token - The bearer token string.
   */
  public setbearToken(token: string)
  {
    this.bearerToken.next(token);
  }

  /**
   * Retrieves the current bearer token.
   * @returns The bearer token string.
   */
  public GetToken(): Observable<string>
  {
    return this.bearerToken.asObservable();
  }

/**
 * Creates request options for HTTP calls.
 *
 * @param options - An object containing optional HTTP settings:
 *   - headers: Custom HTTP headers.
 *   - observe: Whether to return the full HTTP response or just the body. Defaults to `false`.
 *   - httpParams: URL parameters to include in the request.
 *   - responseType: The expected response type (e.g., 'json', 'text').
 *   - withCredentials: Whether to include credentials such as cookies. Defaults to `false`.
 *
 * This function uses **parameter destructuring**, which allows callers to specify only the options they need,
 * without worrying about argument order. For example:
 *
 * ```ts
 * createOptions({
 *   headers: myHeaders,
 *   withCredentials: true
 * });
 * ```
 *
 * @returns A configured options object suitable for use with Angular's HttpClient methods.
 */
createOptions({
              headers,
              observe = false,
              httpParams,
              responseType,
              withCredentials = false
            })
{
  const options: any = {};

  if (headers) options.headers = headers;
  if (observe) options.observe = 'response';
  if (httpParams) options.params = httpParams;
  if (responseType) options.responseType = responseType;
  if (withCredentials) options.withCredentials = true;

  return options;
}

/**
 * Performs a GET request to the specified API endpoint.
 *
 * @param baseUrl - The API base URL type.
 * @param apiUri - The specific API endpoint.
 * @param returnError - Flag to indicate whether to return error responses instead of throwing.
 * @param observeResponse - Flag to indicate whether to return the full HTTP response (including headers/status) instead of just the body.
 * @param requestType - Optional request type indicator (e.g., for logging or custom headers).
 * @param httpParams - Optional HTTP parameters to include in the request.
 * @param withCredentials - If true, credentials such as cookies or authorization headers will be sent with the request.
 *                          This is necessary for cross-origin requests that rely on session cookies or other credentials.
 *
 * @returns Observable of the GET response.
 */
public Get<T>(baseUrl: ApiTypes,
              apiUri: string,
              returnError: boolean = false,
              observeResponse: boolean = false,
              requestType?: string,
              httpParams?: HttpParams,
              withCredentials: boolean = false): Observable<any> {

  const requestOptions =  this.createOptions({headers: this.getBaseHeader(baseUrl,apiUri),
                                              observe: observeResponse,
                                              httpParams: httpParams,
                                              responseType: requestType,
                                              withCredentials: withCredentials});

  return this.httpClient.get<T>(this.envUrlFromType(baseUrl) + apiUri, requestOptions).pipe(
    catchError((err) => {
      let errMsg: string;
      if (err.error instanceof ErrorEvent) {
        errMsg = 'Error: ${err.error.message}';
      } else {
        errMsg = this.getServerErrorMessage(err);
      }

      if (returnError==true) {
        return of(err);
      }
      else {
        return throwError(() => errMsg);
      }
    })
  );
}

/**
 * Performs a POST request to the specified API endpoint.
 *
 * @param baseUrl - The API base URL type.
 * @param apiUri - The specific API endpoint path.
 * @param body - The request payload, typically a JSON string or FormData object.
 * @param observeResponse - Flag to indicate whether to return the full HTTP response (including headers/status) instead of just the body.
 * @param returnError - Flag to indicate whether to return error responses instead of throwing.
 * @param requestType - Optional request type indicator (e.g., for custom headers or logging).
 * @param httpParams - Optional HTTP parameters to include in the request.
 * @param withCredentials - If true, credentials such as cookies or authorization headers will be sent with the request.
 *                          This is necessary for cross-origin requests that rely on session cookies or other credentials.
 *
 * @returns Observable of the POST response.
 */
public Post<T>(baseUrl: ApiTypes,
               apiUri: string,
               body: string | FormData,
               observeResponse: any = null,
               returnError: boolean = false,
               requestType?: string,
               httpParams?: HttpParams,
               withCredentials: boolean = false
): Observable<any> {

    const requestOptions = this.createOptions({headers: this.getBaseHeader(baseUrl,apiUri),
                                               observe: observeResponse,
                                               httpParams: httpParams,
                                               responseType: requestType,
                                               withCredentials: withCredentials});

    return this.httpClient.post<T>(this.envUrlFromType(baseUrl) + apiUri, body, requestOptions)
      .pipe(
      catchError((err) => {
        let errMsg: string;
        if (err.error instanceof ErrorEvent) {
          errMsg = 'Error: ${err.error.message}';
        } else {
          errMsg = this.getServerErrorMessage(err);
        }
        if (returnError==true) {
          return of(err);
        }
        else {
          return throwError(() => errMsg);
        }
      })
    );
  }

/**
 * Performs a PUT request to the specified API endpoint.
 *
 * @param baseUrl - The API base URL type.
 * @param apiUri - The specific API endpoint path.
 * @param body - The request payload, typically a JSON string or FormData object.
 * @param observeResponse - Flag to indicate whether to return the full HTTP response (including headers/status) instead of just the body.
 * @param returnError - Flag to indicate whether to return error responses instead of throwing.
 * @param requestType - Optional request type indicator (e.g., for custom headers or logging).
 * @param httpParams - Optional HTTP parameters to include in the request.
 * @param withCredentials - If true, credentials such as cookies or authorization headers will be sent with the request.
 *                          This is necessary for cross-origin requests that rely on session cookies or other credentials.
 *
 * @returns Observable of the PUT response.
 */
public Put<T>(baseUrl: ApiTypes,
              apiUri: string,
              body: string | FormData,
              observeResponse: any = null,
              returnError: boolean = false,
              requestType?: string,
              httpParams?: HttpParams,
              withCredentials: boolean = false
): Observable<any> {

    const requestOptions = this.createOptions({headers: this.getBaseHeader(baseUrl,apiUri),
                                               observe: observeResponse,
                                               httpParams: httpParams,
                                               responseType: requestType,
                                               withCredentials: withCredentials});

    return this.httpClient.put<T>(this.envUrlFromType(baseUrl) + apiUri, body, requestOptions)
      .pipe(
      catchError((err) => {
        let errMsg: string;
        if (err.error instanceof ErrorEvent) {
          errMsg = 'Error: ${err.error.message}';
        } else {
          errMsg = this.getServerErrorMessage(err);
        }
        if (returnError==true) {
          return of(err);
        }
        else {
          return throwError(() => errMsg);
        }
      })
    );
  }

/**
 * Performs a DELETE request to the specified API endpoint.
 *
 * @param baseUrl - The API base URL type.
 * @param apiUri - The specific API endpoint path.
 * @param observeResponse - Flag to indicate whether to return the full HTTP response (including headers/status) instead of just the body.
 * @param returnError - Flag to indicate whether to return error responses instead of throwing.
 * @param requestType - Optional request type indicator (e.g., for custom headers or logging).
 * @param httpParams - Optional HTTP parameters to include in the request.
 * @param withCredentials - If true, credentials such as cookies or authorization headers will be sent with the request.
 *                          This is necessary for cross-origin requests that rely on session cookies or other credentials.
 *
 * @returns Observable of the DELETE response.
 */
public Delete<T>(baseUrl: ApiTypes,
                apiUri: string,
                observeResponse: any = null,
                returnError: boolean = false,
                requestType?: string,
                httpParams?: HttpParams,
                withCredentials: boolean = false
): Observable<any> {

    const requestOptions = this.createOptions({headers: this.getBaseHeader(baseUrl,apiUri),
                                               observe: observeResponse,
                                               httpParams: httpParams,
                                               responseType: requestType,
                                               withCredentials: withCredentials});

    return this.httpClient.delete<T>(this.envUrlFromType(baseUrl) + apiUri, requestOptions)
      .pipe(
      catchError((err) => {
        let errMsg: string;
        if (err.error instanceof ErrorEvent) {
          errMsg = 'Error: ${err.error.message}';
        } else {
          errMsg = this.getServerErrorMessage(err);
        }
        if (returnError==true) {
          return of(err);
        }
        else {
          return throwError(() => errMsg);
        }
      })
    );
  }

  /**
   * Constructs the base header for API requests.
   * @param apiType - The API type.
   * @param apiUriPath - The API URI path.
   * @returns The constructed HttpHeaders.
   */
  private getBaseHeader(apiType: ApiTypes, apiUriPath: string=""): HttpHeaders {
    let header = new HttpHeaders();

    if (this.envUrlFromType(apiType)!="")
    {
      if (this.apiToken && (this.nextTimeToCheckExpire < new Date()) && this.isTokenExpired(this.apiToken)) {
        this.apiToken = this.LoadValidSessionObj();
      } else if (!this.apiToken) {
        this.apiToken = this.LoadValidSessionObj();
      }
    }

    //token should only be applied to our api endpoints
    if (this.apiToken && this.apiToken!="")
    {
      header = header.set("Authorization", "Bearer " + this.apiToken)
    }

    //header = header.set('Host', this.getHost(apiUri));
    //header.set('Host', "localhost");
    header = header.set("Content-Type", 'application/json');
    //header = header.set("User-Agent", this.browserInfo);

    let pageAudit = this.auditService.getPageAudit();
    header = header.set("AuditRef", pageAudit.guid || "")
    header = header.set("AuditPage", pageAudit.page || "");
    header = header.set('AuditSiteId', pageAudit.siteId || "");

    // Sanitize audit parameters to prevent Unicode characters in HTTP headers
    const sanitizedParams = this.sanitizeUnicodeForHeaders(pageAudit.parameters || "");
    header = header.set('AuditParams', JSON.stringify(sanitizedParams));

    return header;
  }

  /**
   * Sanitize Unicode characters that might cause HTTP header encoding issues
   * @param data - The data to sanitize
   * @returns Sanitized data with Unicode characters replaced by ASCII equivalents
   */
  private sanitizeUnicodeForHeaders(data: any): any {
    if (typeof data === 'string') {
      // Replace common Unicode characters with ASCII equivalents
      return data
        .replace(/–/g, '-')  // En dash to hyphen
        .replace(/—/g, '-')  // Em dash to hyphen
        .replace(/'/g, "'")  // Smart quote to straight quote
        .replace(/'/g, "'")  // Smart quote to straight quote
        .replace(/"/g, '"')  // Smart quote to straight quote
        .replace(/"/g, '"')  // Smart quote to straight quote
        .replace(/…/g, '...') // Ellipsis to three dots
        .replace(/®/g, '(R)') // Registered trademark
        .replace(/™/g, '(TM)') // Trademark
        .replace(/°/g, ' deg') // Degree symbol
        .replace(/[^\x00-\xFF]/g, '?'); // Replace any remaining non-ISO-8859-1 characters with ?
    } else if (Array.isArray(data)) {
      return data.map(item => this.sanitizeUnicodeForHeaders(item));
    } else if (data && typeof data === 'object') {
      const sanitized: any = {};
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          sanitized[key] = this.sanitizeUnicodeForHeaders(data[key]);
        }
      }
      return sanitized;
    }
    return data;
  }

  /**
   * Translates server errors into human-readable format.
   * @param error - The HttpErrorResponse object.
   * @returns A string representation of the error message.
   */
  private getServerErrorMessage(error: HttpErrorResponse): string {
    switch (error.status) {
      case 404: {
        return `Not Found: ${error.message}`;
      }
      case 403: {
        return `Access Denied: ${error.message}`;
      }
      case 500: {
        return `Internal Server Error: ${error.message}`;
      }
      default: {
        return `Unknown Server Error: ${error.message}`;
      }
    }
  }

  /**
   * Converts an API type into its corresponding environment URL.
   * @param apiType - The API type.
   * @returns The corresponding environment URL.
   */
  public envUrlFromType(apiType: ApiTypes): string
  {
    switch(apiType) {
      case ApiTypes.V2:
        return environment.apiV2;
      case ApiTypes.AuthProvider:
        return environment.authProvider;
      case ApiTypes.V1:
        return environment.apiV1;
      case ApiTypes.PdfFiller:
        return environment.pdfFiller;
      default:
        return "";
    }

  }

  /** CURRENT NOT USED **
   * Extracts the host from a given base URL.
   * @param baseUrl - The base URL.
   * @returns The host string.
   */
  private getHost(baseUrl: string): string
  {
    let host = "";
    let pieces = baseUrl.split('/',3);
    host = `${pieces[0]}/${pieces[1]}/${pieces[2]}`
    return host;
  }

  public convertUtcStringToDatePlusMin(expiresUtcString: string, min: number = 0, offset: number = 0): Date {

    let offsetToExpiration = this.percentToNearestWholeNumber(min, 15)
    if (offset > 0)
    {
      offsetToExpiration += offset;
    }

    const parsedExpiresDate = moment.utc(expiresUtcString, 'M/D/YYYY h:mm:ss A');
    const convertedExpiresDate = parsedExpiresDate.toDate();
    convertedExpiresDate.setMinutes(convertedExpiresDate.getMinutes() - offsetToExpiration);

    return convertedExpiresDate;
  }

  public percentToNearestWholeNumber(integer: number, percent: number): number {
    // Ensure percent is within the valid range (0-100)
    if (percent < 0 || percent > 100) {
      throw new Error('Percentage must be between 0 and 100.');
    }

    // Calculate the actual value represented by the percentage
    const actualValue = (percent / 100) * integer;

    // Round the actual value to the nearest whole number
    const roundedValue = Math.round(actualValue);

    return roundedValue;
  }

  // Loads and validates the session object from local storage
  public LoadValidSessionObj(): string {
    let sessionObj = localStorage.getItem('Session');
    if (sessionObj) {
      let session = JSON.parse(sessionObj)
      return session.access_token;

    }
    return '';
  }

  public isSessionValid(session: any): { isValid: boolean; minutesLeft: number } {
    const currentTime = new Date().getTime();
    const sessionExpiresTime = this.convertUtcStringToDatePlusMin(
      session.expires
    ).getTime();

    const isValid = currentTime <= sessionExpiresTime;
    const minutesLeft = isValid
      ? Math.floor((sessionExpiresTime - currentTime) / (1000 * 60))
      : 0;

    return { isValid, minutesLeft };
  }

  isTokenExpired(token: string): boolean {
    // Splitting the JWT token into its parts: header, payload, signature
    const tokenParts = token.split('.').slice(0, 2);

    // Decoding the payload
    const payload = tokenParts[1];
    const decodedPayload = JSON.parse(window.atob(payload));

    if (!decodedPayload.exp) {
      return false;
    }

    const date = new Date(0);
    date.setUTCSeconds(decodedPayload.exp);
    this.nextTimeToCheckExpire = date;
    return !(date.valueOf() > new Date().valueOf());
  }

}
