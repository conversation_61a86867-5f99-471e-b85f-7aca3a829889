// Quality Measures Dashboard with Left Stacked Panels
.report-wrapper {
  display: flex;
  flex-direction: column;
  height: 96vh;
  font-family: MuseoSans-300;
  background-color: #eff1f6;
  margin-top: -20px;
}

// Report Header
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: transparent;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .report-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 1.75rem;
      font-weight: 500;
    }
  }
}

// Report Content Layout
.report-content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  padding-top: 8px;
}

// Left Sidebar Container
.left-sidebar {
  width: 275px;
  display: flex;
  flex-direction: column;
  gap: 0px; // Remove gap so panels touch each other
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%; // Constrain to parent height
  min-height: 0; // Allow shrinking below content size
  overflow: hidden; // Prevent overflow from sidebar container
}

// Sidebar Panel Base Styles - Wrapper approach for dual border
.sidebar-panel {
  // Outer wrapper for gray border
  border: 1px solid #A9A9A9;
  border-radius: 0; // Default: no border radius for middle panels
  padding: 0px; // Space between border and inner panel
  margin-top: -1px; // Overlap the top border with the panel above
  transition: flex 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // Default: panel takes full available space when open
  flex: 1;
  min-height: 0; // Critical: Allow wrapper to shrink below content size
  display: flex; // Make wrapper a flex container
  flex-direction: column; // Stack content vertically

  // When collapsed, panel takes minimal space (header only)
  &.collapsed {
    flex: 0 0 auto;
  }

  // First panel: curved top corners, straight bottom corners
  &:first-child {
    margin-top: 0;
    border-radius: 16px 16px 0 0; // More rounded corners
  }

  // Last panel: straight top corners, curved bottom corners
  &:last-child {
    border-radius: 0 0 16px 16px; // More rounded corners
  }

  // If there's only one panel, keep all corners curved
  &:first-child:last-child {
    border-radius: 16px; // More rounded corners
  }

  // Inner panel with original styling
  .panel-inner {
    background-color: #fff;
    border: 8px solid #e0e0e0;
    border-radius: inherit; // Inherit border radius from wrapper
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex: 1; // Take all available space in wrapper
    min-height: 0; // Allow flex child to shrink below content size
  }

  // Remove inner border where panels connect
  &:not(:first-child) .panel-inner {
    border-top: none; // Remove top border on panels that aren't first
  }

  &:not(:last-child) .panel-inner {
    border-bottom: none; // Remove bottom border on panels that aren't last
  }

  // Panel header now inside the inner panel
  .panel-inner .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;

    &:hover {
      background-color: #f8f9fa;
    }

    h3 {
      margin: 0;
      font-family: Museo500-Regular;
      font-size: 1.3rem;
      font-weight: 500;
      color: #0071BC;
    }

    .panel-toggle-btn {
      margin-right: -20px;
      color: #666;
      background-color: transparent !important;
      border: none !important;
      outline: none !important;
      box-shadow: none !important;

      &:hover {
        background-color: transparent !important;
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
      }

      &:focus {
        background-color: transparent !important;
        border: none !important;
        outline: 2px solid #0071BC !important;
        outline-offset: 2px !important;
        box-shadow: none !important;
      }

      &:active {
        background-color: transparent !important;
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
      }

      // Override the specific MDC classes that cause the circular hover effect
      .mat-mdc-button-persistent-ripple,
      .mdc-icon-button__ripple {
        background-color: transparent !important;
        display: none !important;
        opacity: 0 !important;
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}

// Global override for the specific MDC classes that cause the circular hover effect
::ng-deep .panel-header .panel-toggle-btn {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background-color: transparent !important;

  &:hover,
  &:focus,
  &:active {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background-color: transparent !important;
  }

  &:focus {
    outline: 2px solid #0071BC !important;
    outline-offset: 2px !important;
  }

  .mat-mdc-button-persistent-ripple,
  .mdc-icon-button__ripple,
  .mat-ripple,
  .mat-button-ripple,
  .mat-button-focus-overlay {
    background-color: transparent !important;
    display: none !important;
    opacity: 0 !important;
  }

  // Override Material UI button classes
  &.mat-mdc-icon-button,
  &.mat-icon-button,
  &.mdc-icon-button {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background-color: transparent !important;

    &:hover,
    &:focus,
    &:active {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;
      background-color: transparent !important;
    }
  }
}

.sidebar-panel {
  .panel-inner .panel-content {
    flex: 1;
    overflow: hidden;
    min-height: 0; // Allow flex child to shrink below content size

    &.hidden {
      display: none;
    }
  }
}

// Reports Panel Specific Styles
.reports-panel {
  // Reports panel takes all available space
  flex: 1;

  .panel-inner .panel-content {
    overflow-y: auto;
    min-height: 0; // Allow flex child to shrink below content size

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }


}

// Filters Panel Specific Styles
.filters-panel {
  // Filters panel only takes the space it needs
  flex: 0 0 auto;

  .panel-inner .panel-content {
    // Allow content to determine height
    flex: 0 0 auto;
    // Remove any default padding to let widget stretch to edges
    padding: 0;
  }

  .filters-container {
    // Remove padding to let widget stretch to edges
    padding: 0;
    background-color: #f8f9fa;
    // Take full height of panel content
    height: 100%;
    // Remove fixed height - let content determine size
    min-height: 200px; // Minimum height for usability
    max-height: 600px; // Maximum height before scrolling
    overflow-y: auto;

    // Custom scrollbar styling for container
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .filters-widget {
    background-color: white;
    // Remove border and border-radius to stretch to panel edges
    border: none;
    border-radius: 0;
    box-shadow: none;
    // Add padding inside the widget instead of container
    padding: 0;
    width: 100%;
    height: 100%;

    // Widget stretches to fill the entire filters container
    display: flex;
    flex-direction: column;

    // Ensure the widget content doesn't overflow
    overflow: hidden;
  }
}

// Report Viewer Area (Right)
.report-viewer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f8f9fa; // Match main background color
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  // Ensure child components take full height
  > * {
    height: 100%;
    flex: 1;
  }
}

// Quality Measures Specific Styles
.quality-measures-container {
  background-color: #efefef;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  margin-left: 10px;
  margin-right: 10px;
  flex-shrink: 0; // Don't shrink the header

  .header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .qualifying-patients {
      font-size: 1rem;
      color: #666;
      text-align: center;
    }

    .export-dropdown {
      position: absolute;
      right: 0;
    }
  }
}

// Export dropdown styling - keeping only the positioning wrapper
// Custom dropdown styles removed - now using reusable download-menu component

.table-notes {
  border-radius: 12px;
  background-color: #f9f9f9;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-notes {
  border-top: 2px solid #d8d8d8;
  border-left: 2px solid #cccccc;
  border-right: 2px solid #cccccc;
  border-bottom: 2px solid #cccccc;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  background-color: #f2f2f2;
  color: #666666;
  padding: 10px;
  overflow-y: auto;

  h3 {
    color: #0071bc;
    font-family: "MuseoSans-500", "sans-serif";
    font-size: 1.1rem;
    margin-bottom: 10px;
  }

  ul {
    margin-bottom: 15px;

    li {
      margin-bottom: 8px;
      line-height: 1.4;

      &.score-item {
        margin-bottom: 6px;
      }
    }
  }

  a {
    color: #0077cc;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .opera-score-title {
    font-weight: bold;
    font-size: large;
  }
}

// Table and icon styles
.filled-satisfied-icon {
  color: #99d2f8;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}

.filled-unsatisfied-icon {
  color: #0089e3;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}

.measures-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
}

.number {
  text-align: right;
  padding-right: 20px;
}
