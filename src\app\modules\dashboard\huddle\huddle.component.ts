import { Component, EventEmitter, Output } from '@angular/core';
import { HuddleDetail } from '../../shared-modules/layouts/models/huddle-detail.model';
import { HuddleCalendar } from '../../shared-modules/layouts/models/huddle-calendar.model';
import { HuddleFilter } from '../../shared-modules/layouts/models/huddle-filter.model';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { formatDate } from '@angular/common';
import { LayoutService } from '../../shared-modules/layouts/services/layout/layout.service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { IChorusAccessViewModel, RoleTypes } from 'src/app/shared-services/user-context/models/user-security-model';

@Component({
  selector: 'epividian-huddle',
  templateUrl: './huddle.component.html',
  styleUrl: './huddle.component.scss',
})
export class HuddleComponent {

  public siteId = "";
  public isSiteDirector = false;
  public selectedFilter: string = "";
  public huddleAppointments: HuddleDetail[] = [];
  public huddleAllAppointments: HuddleDetail[] = [];
  public calendarSelection: HuddleCalendar = {} as HuddleCalendar;
  public selectedFilterObject: HuddleFilter | null = null;
  public pageSubscriptions: Subscription = new Subscription;
  public safeToLoad: boolean = false;

  constructor(
    private layoutService: LayoutService,
    private userContext: UserContext,
  ){}

  async ngOnInit() {
    // Show spinner initially - child components will hide it when they're done loading
    // Only show spinner if we don't already have data loaded
    if (!this.huddleAppointments || this.huddleAppointments.length === 0) {
      this.layoutService.showSpinner();
    }
    await this.checkSiteDirectorAccess();
  }

  ngOnDestroy(): void {
    this.pageSubscriptions.unsubscribe();
  }
 
  /**
   * Check if the user has Site Director role access
   * Set the isSiteDirector flag for child components to use
   */
  private checkSiteDirectorAccess(): void {
    this.pageSubscriptions.add(
      this.userContext.getUserSecurity().subscribe((userAccess: IChorusAccessViewModel[]) => {
        this.setSiteDirectorRole(userAccess);
      })
    );
  }
 
  /**
   * Determine if user has Site Director role and set the flag
   */
  private setSiteDirectorRole(userAccess: IChorusAccessViewModel[]): void {
    // If no user access data is available, default to false
    if (userAccess.length > 0) {
      const currentSiteId = this.userContext.GetCurrentSiteValue();
      this.siteId = currentSiteId.toString();
      // Find the access entry that matches the current siteId
      const siteAccess = userAccess.find(access => access.siteId === currentSiteId);
  
      if (!siteAccess || !siteAccess.userRoles || siteAccess.userRoles.length === 0) {
        this.isSiteDirector = false;
        this.safeToLoad = true;
        return;
      }
  
      // Check if the user has the Site Director role
      this.isSiteDirector = siteAccess.userRoles.some(r => r.role === RoleTypes.Site_Director);
      this.safeToLoad = true;
    }
  }


  onFilterObjectSelected(filterObject: HuddleFilter) {
    this.selectedFilterObject = filterObject;
    this.selectedFilter = filterObject.name; // Extract the name from the filter object
  }

  onHuddleAppointments(appointments: HuddleDetail[]){
    this.huddleAppointments = appointments;
  }

  onHuddleAllAppointments(allAppointments: HuddleDetail[]){
    this.huddleAllAppointments = allAppointments; 
  }

  onCalendarSelected(calendarSelected: HuddleCalendar){
    this.calendarSelection = calendarSelected;
  }

  async printReport() {
    try {
      this.generateHuddlePDF();
    } catch (error) {
      console.error('Error generating huddle PDF:', error);
    }
  }

  private generateHuddlePDF() {
    const doc = new jsPDF('l', 'pt', 'letter'); // Changed to landscape orientation
    const currentDate = new Date();
    const formattedDate = formatDate(currentDate, 'MM/dd/yyyy', 'en-US');
    const formattedTime = formatDate(currentDate, 'HH:mm', 'en-US');

    let yPosition = 50;

    // Header - Title with filter name
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    const title = `Huddle - ${this.selectedFilter}`;
    doc.text(title, 50, yPosition);

    yPosition += 20;

    // Subtitle with location and provider info
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    let subtitle = 'patients on the schedule';

    // Add location if selected
    if (this.calendarSelection.location?.name) {
      subtitle += ` at ${this.calendarSelection.location.name}`;
    }

    // Add provider if selected
    if (this.calendarSelection.provider && this.calendarSelection.provider.trim() !== '') {
      // Get provider name from appointments data instead of using ID
      const providerName = this.getProviderNameFromAppointments(this.calendarSelection.provider);
      if (providerName) {
        subtitle += ` for ${providerName}`;
      }
    }

    doc.text(subtitle, 50, yPosition);
    yPosition += 25;

    // Filter description removed to avoid duplication with subtitle above

    // Content section header - appointments count
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 113, 188); // Blue color #0071BC
    const appointmentCount = this.huddleAppointments.length;
    const uniquePatients = new Set(this.huddleAppointments.map(apt => apt.demographicsId)).size;
    const appointmentHeader = `${appointmentCount} Appointments for ${uniquePatients} Patients`;
    doc.text(appointmentHeader, 50, yPosition);

    yPosition += 25;

    // Date info only (location and provider are now in subtitle)
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(0, 0, 0); // Reset to black
    const selectedDate = formatDate(this.calendarSelection.date || new Date(), 'MMMM d, y', 'en-US');
    doc.text(selectedDate, 50, yPosition);

    yPosition += 30;

    // Prepare table data
    const tableData = this.huddleAppointments.map(appointment => [
      formatDate(appointment.scheduleTime || appointment.scheduleDt, 'h:mm a', 'en-US'),
      this.getAppointmentType(appointment.appointmentType),
      appointment.statusDescription || '',
      (appointment.providerName || '').replace("_", " "),
      appointment.patientFullName,
      appointment.mrn,
      appointment.measureGap?.toString() || '0',
      this.formatDetailsForPDF(appointment.details)
    ]);

    // Generate table
    autoTable(doc, {
      head: [['Time', 'Type', 'Status', 'Provider', 'Patient Name', 'Patient MRN', '# Quality Gaps', 'Details']],
      body: tableData,
      startY: yPosition,
      styles: {
        fontSize: 10,
        cellPadding: 4,
        overflow: 'linebreak',
        cellWidth: 'wrap'
      },
      headStyles: {
        fillColor: [0, 113, 188], // Blue header
        textColor: [255, 255, 255], // Ensure white text
        fontStyle: 'bold',
        halign: 'left',
        valign: 'middle',
        lineWidth: 0.1,
        lineColor: [0, 113, 188]
      },
      columnStyles: {
        0: { cellWidth: 60 }, // Time
        1: { cellWidth: 80 }, // Type
        2: { cellWidth: 70 }, // Status
        3: { cellWidth: 85 }, // Provider
        4: { cellWidth: 100 }, // Patient Name
        5: { cellWidth: 60 }, // MRN
        6: { cellWidth: 40 }, // Quality Gaps
        7: {
          cellWidth: 230, // Details - increased to better fill the page
          cellPadding: { left: 5, right: 5, top: 3, bottom: 3 }
        }
      },
      margin: { left: 30, right: 30, bottom: 60 }, // Add bottom margin for footer
      pageBreak: 'auto',
      showHead: 'everyPage',
      theme: 'plain', // Ensure consistent styling across pages
      didDrawCell: (data: any) => {
        // Force header styling on every page
        if (data.row.section === 'head') {
          data.cell.styles.fillColor = [0, 113, 188]; // Blue header
          data.cell.styles.textColor = [255, 255, 255]; // White text
          data.cell.styles.fontStyle = 'bold';
          data.cell.styles.halign = 'left';
          data.cell.styles.valign = 'middle';
          data.cell.styles.lineWidth = 0.1;
          data.cell.styles.lineColor = [0, 113, 188];
        } else {
          // Apply alternating row colors for body rows
          if (data.row.index % 2 === 0) {
            data.cell.styles.fillColor = [245, 245, 245]; // Light gray for even rows
          } else {
            data.cell.styles.fillColor = [255, 255, 255]; // White for odd rows
          }

          // Special formatting for Details column (index 7)
          if (data.column.index === 7) {
            data.cell.styles.valign = 'top';
            data.cell.styles.lineWidth = 0.1;
          }
        }
      }
    });

    // Footer
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);

      // Page number
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Page ${i} of ${pageCount}`, 50, doc.internal.pageSize.height - 50);

      // CHORUS branding
      doc.text('CHORUS™ by Epividian®', doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 50, { align: 'center' });

      // Print date
      doc.text(`Printed: ${formattedDate}`, doc.internal.pageSize.width - 50, doc.internal.pageSize.height - 50, { align: 'right' });
    }

    // Save the PDF
    doc.save(`Huddle_${this.selectedFilter.replace(/\s+/g, '_')}_${formattedDate.replace(/\//g, '-')}.pdf`);
  }

  private formatDetailsForPDF(details: string[]): string {
    if (!details || details.length === 0) return '';

    // Join details with bullet points and proper line breaks
    // Remove existing bullets first, then add our own
    return details
      .map(detail => {
        // Remove existing bullet points and trim
        const cleanDetail = detail.replace(/^[•·\-\*]\s*/, '').trim();
        return `• ${cleanDetail}`;
      })
      .join('\n');
  }

  private getStatus(status: string, appointmentType: string): string {
    if (appointmentType == "Telehealth" && !this.isNoShowOrCanceled(status)){
      return "telehealth";
    }
    return status.replace(/[\s&-]+/g, '').toLowerCase();
  }

  private isNoShowOrCanceled(statusDesc: string): boolean {
    if(statusDesc == 'No Show'){
      return true;
    }
    if (statusDesc.includes("Canceled")){
      return true;
    }
    return false;
  }

  private getAppointmentType(appointmentType: string): string {
    if (appointmentType != null){
      if(appointmentType.length > 22){
        return appointmentType.slice(0,22) + "...";
      }
    }
    return appointmentType;
  }

  private getProviderNameFromAppointments(providerId: string): string {
    // Find the first appointment that matches the provider ID and get the provider name
    const appointment = this.huddleAppointments.find(apt =>
      apt.providerId?.toString() === providerId
    );
    return appointment?.providerName || providerId; // Fallback to ID if name not found
  }
}
