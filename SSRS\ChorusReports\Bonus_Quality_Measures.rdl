<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.14.36414.22</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.14.36414.22</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2025-09-25T12:50:11.8656097Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="SITE_INFO">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText> DECLARE @SITE_ID INT
          SET                @SITE_ID =
          (SELECT        SITE_ID
                               FROM            CLEAN.SITE)
          SELECT TOP 1        SITE_NM, CITY_TXT, STATE_TXT,IIF(OS.SITE_ID IS NULL ,0,1)IS_OPERA
          FROM            [CHORUS].[ADMIN].[SITE] CS LEFT OUTER JOIN   OPERA.CLEAN.SITE OS ON CS.SITE_ID = OS.SITE_ID
          WHERE        (STATUS_CD = 'A') AND (CS.SITE_ID = @SITE_ID)</CommandText>
      </Query>
      <Fields>
        <Field Name="SITE_NM">
          <DataField>SITE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CITY_TXT">
          <DataField>CITY_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="STATE_TXT">
          <DataField>STATE_TXT</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="IS_OPERA">
          <DataField>IS_OPERA</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>
          SELECT EXTRACT_DT
          FROM CLEAN.SITE
        </CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="DS_BONUS_MEASURES">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@USER_ID">
            <Value>=Parameters!USER_ID.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@REPORTING_PERIOD">
            <Value>=Parameters!REPORTING_YEAR.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@locationCd">
            <Value>=Parameters!locationCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@providerCd">
            <Value>=Parameters!providerCd.Value</Value>
          </QueryParameter>
          <QueryParameter Name="@COHORT_ID">
            <Value>=Parameters!COHORT_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>SELECT ROWNUM,  MEASURE_ID, MEASURE_CD, REPORT_ID, COHORT_NAME, LOCATION, PRIMARY_PROVIDER, REPORTING_PERIOD, ROLLING_WEEK, INVERTED_FLG, COHORT_DESCRIPTION, 
TOT_COHORT_CNT,TOT_SCHED_CNT, TOT_VISIT_CNT, MEASURE_NM, MEASURE_ORD, COHORT_CNT, REPORT_LEVEL_DESC_1, REPORT_LEVEL_DESC_2, 
REPORT_LEVEL_DESC_3,  MEETING_DESC, NOT_MEETING_DESC, COHORT_ID, DISPLAY_ORDER_NO
FROM [REPORT].[GET_BONUS](@USER_ID,  @REPORTING_PERIOD , @locationCd, @providerCd, @COHORT_ID) ORDER BY DISPLAY_ORDER_NO</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="ROWNUM">
          <DataField>ROWNUM</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ID">
          <DataField>MEASURE_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_CD">
          <DataField>MEASURE_CD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_ID">
          <DataField>REPORT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="COHORT_NAME">
          <DataField>COHORT_NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LOCATION">
          <DataField>LOCATION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PRIMARY_PROVIDER">
          <DataField>PRIMARY_PROVIDER</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORTING_PERIOD">
          <DataField>REPORTING_PERIOD</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ROLLING_WEEK">
          <DataField>ROLLING_WEEK</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="INVERTED_FLG">
          <DataField>INVERTED_FLG</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="COHORT_DESCRIPTION">
          <DataField>COHORT_DESCRIPTION</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TOT_COHORT_CNT">
          <DataField>TOT_COHORT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOT_SCHED_CNT">
          <DataField>TOT_SCHED_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="TOT_VISIT_CNT">
          <DataField>TOT_VISIT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MEASURE_NM">
          <DataField>MEASURE_NM</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEASURE_ORD">
          <DataField>MEASURE_ORD</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="COHORT_CNT">
          <DataField>COHORT_CNT</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_1">
          <DataField>REPORT_LEVEL_DESC_1</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_2">
          <DataField>REPORT_LEVEL_DESC_2</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="REPORT_LEVEL_DESC_3">
          <DataField>REPORT_LEVEL_DESC_3</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="MEETING_DESC">
          <DataField>MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NOT_MEETING_DESC">
          <DataField>NOT_MEETING_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="COHORT_ID">
          <DataField>COHORT_ID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="DISPLAY_ORDER_NO">
          <DataField>DISPLAY_ORDER_NO</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="DS_TITLE">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT [NAME] FROM [REPORT].[REPORT] WHERE REPORT_ID = 23</CommandText>
      </Query>
      <Fields>
        <Field Name="NAME">
          <DataField>NAME</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix5">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.35cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.45cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.17795cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.17795cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.19192cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.63229cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox62">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>#</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox54</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Top</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox63">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>%</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox59</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Top</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>2</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox16</rd:DefaultName>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <LeftBorder>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>0.25pt</Width>
                            </LeftBorder>
                            <BackgroundColor>#1f4e78</BackgroundColor>
                            <VerticalAlign>Top</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.51cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox45">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=iif(Fields!INVERTED_FLG.Value = true,(SUM(Cint(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value))), Sum(CInt(Fields!COHORT_CNT.Value)))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox45</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_ORD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, 0, 1)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, Fields!NOT_MEETING_DESC.Value, Fields!MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FormatNumber(sum(Fields!TOT_COHORT_CNT.Value),0)</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=iif(inscope("LOCATION3"),Fields!LOCATION.Value,iif(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER3"),Fields!PRIMARY_PROVIDER.Value,iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=iif(inscope("LOCATION3"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=iif(inscope("PRIMARY_PROVIDER3"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value = TRUE, SUM(Fields!TOT_COHORT_CNT.Value) - SUM(Fields!COHORT_CNT.Value), SUM(Fields!COHORT_CNT.Value))</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=Parameters!extractDt.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortCnt">
                                      <Value>=Sum(CDbl(Fields!COHORT_CNT.Value))</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="parentLabCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,109,
		Fields!MEASURE_ORD.Value=7,319, 
		Fields!MEASURE_ORD.Value=9,Nothing, 
		Fields!MEASURE_ORD.Value=15,109,
		Fields!MEASURE_ORD.Value=16,109,
		Fields!MEASURE_ORD.Value=17,109)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		Fields!MEASURE_ORD.Value=2,101,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,8, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd2">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,9, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd3">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,Nothing, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortNm">
                                      <Value>=Fields!COHORT_NAME.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox46">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>(</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>=IIF(Fields!INVERTED_FLG.Value = true, Sum(CInt(Fields!COHORT_CNT.Value)), (SUM(CInt(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value))))</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                                <TextRun>
                                  <Value>)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Format>#,0;(#,0)</Format>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox46</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_ORD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, 1, 0)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, Fields!MEETING_DESC.Value, Fields!NOT_MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FORMATNUMBER(SUM(Fields!TOT_COHORT_CNT.Value),0)</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value = TRUE, SUM(Fields!COHORT_CNT.Value), SUM(Fields!TOT_COHORT_CNT.Value) - SUM(Fields!COHORT_CNT.Value))</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=IIF(inscope("LOCATION3"), Fields!LOCATION.Value, IIF(Parameters!drillthroughLocationCd.Value &lt;&gt; "", Parameters!drillthroughLocationCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=IIF(inscope("PRIMARY_PROVIDER3"), Fields!PRIMARY_PROVIDER.Value, IIF(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=IIF(inscope("LOCATION3"),"1","")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=IIF(inscope("PRIMARY_PROVIDER3"), "1", "")</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=Parameters!extractDt.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortCnt">
                                      <Value>=Sum(CInt(Fields!TOT_COHORT_CNT.Value - Fields!COHORT_CNT.Value))</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="parentLabCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,109,
		Fields!MEASURE_ORD.Value=7,319, 
		Fields!MEASURE_ORD.Value=9,Nothing, 
		Fields!MEASURE_ORD.Value=15,109,
		Fields!MEASURE_ORD.Value=16,109,
		Fields!MEASURE_ORD.Value=17,109)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,101,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,8, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd2">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,9, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd3">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,Nothing, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortNm">
                                      <Value>=Fields!COHORT_NAME.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox48">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="(" &amp;
 IIF(Fields!INVERTED_FLG.Value = true,
					 IIf(Format(Round(((SUM(CInt(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value)))/SUM(Fields!TOT_COHORT_CNT.Value))*100)).Contains(".50"),Floor(Round(((SUM(CInt(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value)))/SUM(Fields!TOT_COHORT_CNT.Value))*100)), Round(((SUM(CInt(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value)))/SUM(Fields!TOT_COHORT_CNT.Value))*100)),
					IIf(Format(Round(SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value)*100)).contains(".50"), Floor((SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value))*100), Round((SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value))*100)))
&amp;"%"&amp; ")"</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>CornflowerBlue</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox48</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_ORD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value= TRUE, 0, 1)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value= TRUE, Fields!NOT_MEETING_DESC.Value, Fields!MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FORMATNUMBER(SUM(Fields!TOT_COHORT_CNT.Value), 0)</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value = TRUE, SUM(Fields!TOT_COHORT_CNT.Value) - SUM(Fields!COHORT_CNT.Value), SUM(Fields!COHORT_CNT.Value))</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=IIF(inscope("LOCATION3"),Fields!LOCATION.Value, IIF(Parameters!drillthroughLocationCd.Value &lt;&gt; "",Parameters!drillthroughLocationCd.Value,""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=IIF(inscope("PRIMARY_PROVIDER3"), Fields!PRIMARY_PROVIDER.Value, IIF(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=IIF(inscope("LOCATION3"), "1", "")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=IIF(inscope("PRIMARY_PROVIDER3"), "1", "")</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=Parameters!extractDt.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortCnt">
                                      <Value>=Sum(CDbl(Fields!COHORT_CNT.Value))</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="parentLabCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		  Fields!MEASURE_ORD.Value=2,Nothing,
		  Fields!MEASURE_ORD.Value=3,109,
		  Fields!MEASURE_ORD.Value=7,319, 
		  Fields!MEASURE_ORD.Value=9,Nothing, 
		  Fields!MEASURE_ORD.Value=15,109,
		  Fields!MEASURE_ORD.Value=16,109,
		  Fields!MEASURE_ORD.Value=17,109)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		Fields!MEASURE_ORD.Value=2,101,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,8, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd2">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,9, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd3">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
		Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,Nothing, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortNm">
                                      <Value>=Fields!COHORT_NAME.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox49">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="(" &amp; 
	IIF(Fields!INVERTED_FLG.Value = true,
					 IIf(Format(Round(SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value)*100)).Contains(".50"),Ceiling(Round((SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value))*100)), Round((SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value))*100)),
					IIf(Format(Round((SUM(CInt(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value)))/SUM(Fields!TOT_COHORT_CNT.Value))*100).contains(".50"), Ceiling(((SUM(CInt(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value)))/SUM(Fields!TOT_COHORT_CNT.Value))*100), Round(((SUM(CInt(Fields!TOT_COHORT_CNT.Value)) - SUM(CInt(Fields!COHORT_CNT.Value)))/SUM(Fields!TOT_COHORT_CNT.Value))*100)))
&amp;"%"&amp; ")"</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <Color>Red</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox49</rd:DefaultName>
                          <ActionInfo>
                            <Actions>
                              <Action>
                                <Drillthrough>
                                  <ReportName>Quality_Measures_Detail</ReportName>
                                  <Parameters>
                                    <Parameter Name="measureCd">
                                      <Value>=Fields!MEASURE_ORD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="alertLvl">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, 1, 0)</Value>
                                    </Parameter>
                                    <Parameter Name="guidelineDesc">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value=TRUE, Fields!MEETING_DESC.Value, Fields!NOT_MEETING_DESC.Value)</Value>
                                    </Parameter>
                                    <Parameter Name="totPatients">
                                      <Value>=FORMATNUMBER(SUM(Fields!TOT_COHORT_CNT.Value), 0)</Value>
                                    </Parameter>
                                    <Parameter Name="measureCnt">
                                      <Value>=IIF(Fields!INVERTED_FLG.Value =TRUE, SUM(Fields!COHORT_CNT.Value), SUM(Fields!TOT_COHORT_CNT.Value) - SUM(Fields!COHORT_CNT.Value))</Value>
                                    </Parameter>
                                    <Parameter Name="locationCd">
                                      <Value>=IIF(inscope("LOCATION3"), Fields!LOCATION.Value, IIF(Parameters!drillthroughLocationCd.Value &lt;&gt; "", Parameters!drillthroughLocationCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="providerCd">
                                      <Value>=IIF(inscope("PRIMARY_PROVIDER3"), Fields!PRIMARY_PROVIDER.Value, iif(Parameters!drillthroughProviderCd.Value &lt;&gt; "", Parameters!drillthroughProviderCd.Value, ""))</Value>
                                    </Parameter>
                                    <Parameter Name="toggleLocation">
                                      <Value>=IIF(inscope("LOCATION3"), "1", "")</Value>
                                    </Parameter>
                                    <Parameter Name="toggleProvider">
                                      <Value>=IIF(inscope("PRIMARY_PROVIDER3"), "1", "")</Value>
                                    </Parameter>
                                    <Parameter Name="userId">
                                      <Value>=Parameters!USER_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="reportingPeriod">
                                      <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                    </Parameter>
                                    <Parameter Name="rollingWeek">
                                      <Value>=Fields!ROLLING_WEEK.Value</Value>
                                    </Parameter>
                                    <Parameter Name="COHORT_ID">
                                      <Value>=Fields!COHORT_ID.Value</Value>
                                    </Parameter>
                                    <Parameter Name="inverted_flg">
                                      <Value>=Fields!INVERTED_FLG.Value</Value>
                                    </Parameter>
                                    <Parameter Name="extractDt">
                                      <Value>=Parameters!extractDt.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortCnt">
                                      <Value>=Sum(CInt(Fields!TOT_COHORT_CNT.Value - Fields!COHORT_CNT.Value))</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="parentLabCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,109,
		Fields!MEASURE_ORD.Value=7,319, 
		Fields!MEASURE_ORD.Value=9,Nothing, 
		Fields!MEASURE_ORD.Value=15,109,
		Fields!MEASURE_ORD.Value=16,109,
		Fields!MEASURE_ORD.Value=17,109)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,101,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,8, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd2">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,9, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="labCd3">
                                      <Value>=Switch(Fields!MEASURE_ORD.Value=1,Nothing,
        Fields!MEASURE_ORD.Value=2,Nothing,
		Fields!MEASURE_ORD.Value=3,Nothing,
		Fields!MEASURE_ORD.Value=7,Nothing, 
		Fields!MEASURE_ORD.Value=9,Nothing, 
		Fields!MEASURE_ORD.Value=15,Nothing,
		Fields!MEASURE_ORD.Value=16,Nothing,
		Fields!MEASURE_ORD.Value=17,Nothing)</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                    <Parameter Name="cohortNm">
                                      <Value>=Fields!COHORT_NAME.Value</Value>
                                      <Omit>true</Omit>
                                    </Parameter>
                                  </Parameters>
                                </Drillthrough>
                              </Action>
                            </Actions>
                          </ActionInfo>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>0.5pt</PaddingLeft>
                            <PaddingRight>0.5pt</PaddingRight>
                            <PaddingTop>0.5pt</PaddingTop>
                            <PaddingBottom>0.5pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Rectangle Name="Rectangle2">
                          <ReportItems>
                            <Chart Name="DataBar5">
                              <ChartCategoryHierarchy>
                                <ChartMembers>
                                  <ChartMember>
                                    <Label />
                                  </ChartMember>
                                </ChartMembers>
                              </ChartCategoryHierarchy>
                              <ChartSeriesHierarchy>
                                <ChartMembers>
                                  <ChartMember>
                                    <Label>INVERTED FLG</Label>
                                  </ChartMember>
                                  <ChartMember>
                                    <Label>INVERTED FLG</Label>
                                  </ChartMember>
                                </ChartMembers>
                              </ChartSeriesHierarchy>
                              <ChartData>
                                <ChartSeriesCollection>
                                  <ChartSeries Name="Series">
                                    <ChartDataPoints>
                                      <ChartDataPoint>
                                        <ChartDataPointValues>
                                          <Y>=IIF(Fields!INVERTED_FLG.Value=TRUE,
	SUM(CInt(Fields!TOT_COHORT_CNT.Value - Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value),
	SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value))</Y>
                                        </ChartDataPointValues>
                                        <ChartDataLabel>
                                          <Style />
                                        </ChartDataLabel>
                                        <Style>
                                          <Color>CornflowerBlue</Color>
                                        </Style>
                                        <ChartMarker>
                                          <Style />
                                        </ChartMarker>
                                        <DataElementOutput>Output</DataElementOutput>
                                      </ChartDataPoint>
                                    </ChartDataPoints>
                                    <Type>Bar</Type>
                                    <Subtype>PercentStacked</Subtype>
                                    <Style />
                                    <ChartEmptyPoints>
                                      <Style />
                                      <ChartMarker>
                                        <Style />
                                      </ChartMarker>
                                      <ChartDataLabel>
                                        <Style />
                                      </ChartDataLabel>
                                    </ChartEmptyPoints>
                                    <ValueAxisName>Primary</ValueAxisName>
                                    <CategoryAxisName>Primary</CategoryAxisName>
                                    <ChartSmartLabel>
                                      <CalloutLineColor>Black</CalloutLineColor>
                                      <MinMovingDistance>0pt</MinMovingDistance>
                                    </ChartSmartLabel>
                                  </ChartSeries>
                                  <ChartSeries Name="Series1">
                                    <ChartDataPoints>
                                      <ChartDataPoint>
                                        <ChartDataPointValues>
                                          <Y>=IIF(Fields!INVERTED_FLG.Value=TRUE,
	SUM(CInt(Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value),
	SUM(CInt(Fields!TOT_COHORT_CNT.Value - Fields!COHORT_CNT.Value))/SUM(Fields!TOT_COHORT_CNT.Value))</Y>
                                        </ChartDataPointValues>
                                        <ChartDataLabel>
                                          <Style />
                                        </ChartDataLabel>
                                        <Style>
                                          <Color>Red</Color>
                                        </Style>
                                        <ChartMarker>
                                          <Style />
                                        </ChartMarker>
                                        <DataElementOutput>Output</DataElementOutput>
                                      </ChartDataPoint>
                                    </ChartDataPoints>
                                    <Type>Bar</Type>
                                    <Subtype>PercentStacked</Subtype>
                                    <Style />
                                    <ChartEmptyPoints>
                                      <Style />
                                      <ChartMarker>
                                        <Style />
                                      </ChartMarker>
                                      <ChartDataLabel>
                                        <Style />
                                      </ChartDataLabel>
                                    </ChartEmptyPoints>
                                    <ValueAxisName>Primary</ValueAxisName>
                                    <CategoryAxisName>Primary</CategoryAxisName>
                                    <ChartSmartLabel>
                                      <CalloutLineColor>Black</CalloutLineColor>
                                      <MinMovingDistance>0pt</MinMovingDistance>
                                    </ChartSmartLabel>
                                  </ChartSeries>
                                </ChartSeriesCollection>
                              </ChartData>
                              <ChartAreas>
                                <ChartArea Name="Default">
                                  <ChartCategoryAxes>
                                    <ChartAxis Name="Primary">
                                      <Visible>False</Visible>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption>Axis Title</Caption>
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <Margin>False</Margin>
                                      <ChartMajorGridLines>
                                        <Enabled>False</Enabled>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Minimum>NaN</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                    </ChartAxis>
                                    <ChartAxis Name="Secondary">
                                      <Visible>False</Visible>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption>Axis Title</Caption>
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <Margin>False</Margin>
                                      <ChartMajorGridLines>
                                        <Enabled>False</Enabled>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Location>Opposite</Location>
                                      <Minimum>NaN</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                    </ChartAxis>
                                  </ChartCategoryAxes>
                                  <ChartValueAxes>
                                    <ChartAxis Name="Primary">
                                      <Visible>False</Visible>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption>Axis Title</Caption>
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <ChartMajorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Minimum>0</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                      <rd:SyncScope>Tablix5</rd:SyncScope>
                                      <rd:SyncMaximum>true</rd:SyncMaximum>
                                    </ChartAxis>
                                    <ChartAxis Name="Secondary">
                                      <Visible>False</Visible>
                                      <Style>
                                        <FontSize>8pt</FontSize>
                                      </Style>
                                      <ChartAxisTitle>
                                        <Caption>Axis Title</Caption>
                                        <Style>
                                          <FontSize>8pt</FontSize>
                                        </Style>
                                      </ChartAxisTitle>
                                      <ChartMajorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                          </Border>
                                        </Style>
                                      </ChartMajorGridLines>
                                      <ChartMinorGridLines>
                                        <Style>
                                          <Border>
                                            <Color>Gainsboro</Color>
                                            <Style>Dotted</Style>
                                          </Border>
                                        </Style>
                                      </ChartMinorGridLines>
                                      <ChartMinorTickMarks>
                                        <Length>0.5</Length>
                                      </ChartMinorTickMarks>
                                      <CrossAt>NaN</CrossAt>
                                      <Location>Opposite</Location>
                                      <Minimum>NaN</Minimum>
                                      <Maximum>NaN</Maximum>
                                      <ChartAxisScaleBreak>
                                        <Style />
                                      </ChartAxisScaleBreak>
                                    </ChartAxis>
                                  </ChartValueAxes>
                                  <Style>
                                    <BackgroundColor>#d4d4e5</BackgroundColor>
                                    <BackgroundGradientType>None</BackgroundGradientType>
                                  </Style>
                                  <ChartElementPosition>
                                    <Top>10</Top>
                                    <Left>3</Left>
                                    <Height>80</Height>
                                    <Width>96</Width>
                                  </ChartElementPosition>
                                </ChartArea>
                              </ChartAreas>
                              <Palette>BrightPastel</Palette>
                              <ChartBorderSkin>
                                <Style>
                                  <BackgroundColor>Gray</BackgroundColor>
                                  <BackgroundGradientType>None</BackgroundGradientType>
                                  <Color>White</Color>
                                </Style>
                              </ChartBorderSkin>
                              <ChartNoDataMessage Name="NoDataMessage">
                                <Caption>No Data Available</Caption>
                                <Style>
                                  <BackgroundGradientType>None</BackgroundGradientType>
                                  <TextAlign>General</TextAlign>
                                  <VerticalAlign>Top</VerticalAlign>
                                </Style>
                              </ChartNoDataMessage>
                              <rd:DesignerMode>DataBar</rd:DesignerMode>
                              <DataSetName>DS_BONUS_MEASURES</DataSetName>
                              <Height>0.19685in</Height>
                              <Width>0.86296in</Width>
                              <Style>
                                <Border>
                                  <Color>LightGrey</Color>
                                  <Style>Solid</Style>
                                </Border>
                                <TopBorder>
                                  <Style>None</Style>
                                </TopBorder>
                                <BottomBorder>
                                  <Style>None</Style>
                                </BottomBorder>
                                <LeftBorder>
                                  <Style>None</Style>
                                </LeftBorder>
                                <RightBorder>
                                  <Style>None</Style>
                                </RightBorder>
                                <BackgroundColor>#d4d4e5</BackgroundColor>
                                <BackgroundGradientType>None</BackgroundGradientType>
                              </Style>
                            </Chart>
                          </ReportItems>
                          <KeepTogether>true</KeepTogether>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <BottomBorder>
                              <Color>Silver</Color>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <BackgroundColor>#d4d4e5</BackgroundColor>
                          </Style>
                        </Rectangle>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>5.43503cm</Size>
                    <CellContents>
                      <Textbox Name="Textbox150">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value> Measures</Value>
                                <Style>
                                  <FontFamily>Calibri</FontFamily>
                                  <FontSize>13pt</FontSize>
                                  <FontWeight>Bold</FontWeight>
                                  <Color>White</Color>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox149</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                          <BottomBorder>
                            <Color>#1f4e78</Color>
                            <Style>Solid</Style>
                          </BottomBorder>
                          <BackgroundColor>#1f4e78</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>0.07938cm</Size>
                        <CellContents>
                          <Textbox Name="Textbox40">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value />
                                    <Style>
                                      <FontWeight>Bold</FontWeight>
                                      <Color>White</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Center</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox40</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <BackgroundColor>#1f4e78</BackgroundColor>
                              <VerticalAlign>Top</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>0.07938cm</Size>
                            <CellContents>
                              <Textbox Name="Textbox144">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value />
                                        <Style>
                                          <FontWeight>Bold</FontWeight>
                                          <Color>White</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Center</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox144</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Style>None</Style>
                                  </Border>
                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                  <VerticalAlign>Top</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <TablixHeader>
                                <Size>4.4803cm</Size>
                                <CellContents>
                                  <Textbox Name="Textbox173">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>Location</Value>
                                            <Style>
                                              <FontWeight>Bold</FontWeight>
                                              <Color>White</Color>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Center</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <rd:DefaultName>Textbox173</rd:DefaultName>
                                    <Style>
                                      <Border>
                                        <Style>None</Style>
                                      </Border>
                                      <BackgroundColor>#1f4e78</BackgroundColor>
                                      <VerticalAlign>Middle</VerticalAlign>
                                      <PaddingLeft>2pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>2pt</PaddingTop>
                                      <PaddingBottom>2pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <TablixMembers>
                                <TablixMember>
                                  <TablixHeader>
                                    <Size>0.07938cm</Size>
                                    <CellContents>
                                      <Textbox Name="Textbox87">
                                        <CanGrow>true</CanGrow>
                                        <KeepTogether>true</KeepTogether>
                                        <Paragraphs>
                                          <Paragraph>
                                            <TextRuns>
                                              <TextRun>
                                                <Value />
                                                <Style>
                                                  <FontWeight>Bold</FontWeight>
                                                  <Color>White</Color>
                                                </Style>
                                              </TextRun>
                                            </TextRuns>
                                            <Style>
                                              <TextAlign>Center</TextAlign>
                                            </Style>
                                          </Paragraph>
                                        </Paragraphs>
                                        <rd:DefaultName>Textbox87</rd:DefaultName>
                                        <Style>
                                          <Border>
                                            <Style>None</Style>
                                          </Border>
                                          <BackgroundColor>#1f4e78</BackgroundColor>
                                          <VerticalAlign>Top</VerticalAlign>
                                          <PaddingLeft>2pt</PaddingLeft>
                                          <PaddingRight>2pt</PaddingRight>
                                          <PaddingTop>2pt</PaddingTop>
                                          <PaddingBottom>2pt</PaddingBottom>
                                        </Style>
                                      </Textbox>
                                    </CellContents>
                                  </TablixHeader>
                                  <TablixMembers>
                                    <TablixMember>
                                      <TablixHeader>
                                        <Size>0.07938cm</Size>
                                        <CellContents>
                                          <Textbox Name="Textbox167">
                                            <CanGrow>true</CanGrow>
                                            <KeepTogether>true</KeepTogether>
                                            <Paragraphs>
                                              <Paragraph>
                                                <TextRuns>
                                                  <TextRun>
                                                    <Value />
                                                    <Style>
                                                      <FontWeight>Bold</FontWeight>
                                                      <Color>White</Color>
                                                    </Style>
                                                  </TextRun>
                                                </TextRuns>
                                                <Style>
                                                  <TextAlign>Center</TextAlign>
                                                </Style>
                                              </Paragraph>
                                            </Paragraphs>
                                            <rd:DefaultName>Textbox167</rd:DefaultName>
                                            <Style>
                                              <Border>
                                                <Style>None</Style>
                                              </Border>
                                              <BackgroundColor>#1f4e78</BackgroundColor>
                                              <VerticalAlign>Top</VerticalAlign>
                                              <PaddingLeft>2pt</PaddingLeft>
                                              <PaddingRight>2pt</PaddingRight>
                                              <PaddingTop>2pt</PaddingTop>
                                              <PaddingBottom>2pt</PaddingBottom>
                                            </Style>
                                          </Textbox>
                                        </CellContents>
                                      </TablixHeader>
                                      <TablixMembers>
                                        <TablixMember>
                                          <TablixHeader>
                                            <Size>0.07938cm</Size>
                                            <CellContents>
                                              <Textbox Name="Textbox185">
                                                <CanGrow>true</CanGrow>
                                                <KeepTogether>true</KeepTogether>
                                                <Paragraphs>
                                                  <Paragraph>
                                                    <TextRuns>
                                                      <TextRun>
                                                        <Value />
                                                        <Style>
                                                          <FontWeight>Bold</FontWeight>
                                                          <Color>White</Color>
                                                        </Style>
                                                      </TextRun>
                                                    </TextRuns>
                                                    <Style>
                                                      <TextAlign>Center</TextAlign>
                                                    </Style>
                                                  </Paragraph>
                                                </Paragraphs>
                                                <rd:DefaultName>Textbox185</rd:DefaultName>
                                                <Style>
                                                  <Border>
                                                    <Style>None</Style>
                                                  </Border>
                                                  <BackgroundColor>#1f4e78</BackgroundColor>
                                                  <VerticalAlign>Top</VerticalAlign>
                                                  <PaddingLeft>2pt</PaddingLeft>
                                                  <PaddingRight>2pt</PaddingRight>
                                                  <PaddingTop>2pt</PaddingTop>
                                                  <PaddingBottom>2pt</PaddingBottom>
                                                </Style>
                                              </Textbox>
                                            </CellContents>
                                          </TablixHeader>
                                          <TablixMembers>
                                            <TablixMember>
                                              <TablixHeader>
                                                <Size>8.23844cm</Size>
                                                <CellContents>
                                                  <Textbox Name="Textbox199">
                                                    <CanGrow>true</CanGrow>
                                                    <KeepTogether>true</KeepTogether>
                                                    <Paragraphs>
                                                      <Paragraph>
                                                        <TextRuns>
                                                          <TextRun>
                                                            <Value>Provider</Value>
                                                            <Style>
                                                              <FontWeight>Bold</FontWeight>
                                                              <Color>White</Color>
                                                            </Style>
                                                          </TextRun>
                                                        </TextRuns>
                                                        <Style>
                                                          <TextAlign>Center</TextAlign>
                                                        </Style>
                                                      </Paragraph>
                                                    </Paragraphs>
                                                    <rd:DefaultName>Textbox199</rd:DefaultName>
                                                    <Style>
                                                      <Border>
                                                        <Style>None</Style>
                                                      </Border>
                                                      <BackgroundColor>#1f4e78</BackgroundColor>
                                                      <VerticalAlign>Top</VerticalAlign>
                                                      <PaddingLeft>2pt</PaddingLeft>
                                                      <PaddingRight>2pt</PaddingRight>
                                                      <PaddingTop>2pt</PaddingTop>
                                                      <PaddingBottom>2pt</PaddingBottom>
                                                    </Style>
                                                  </Textbox>
                                                </CellContents>
                                              </TablixHeader>
                                              <TablixMembers>
                                                <TablixMember />
                                              </TablixMembers>
                                            </TablixMember>
                                          </TablixMembers>
                                        </TablixMember>
                                      </TablixMembers>
                                    </TablixMember>
                                  </TablixMembers>
                                </TablixMember>
                              </TablixMembers>
                            </TablixMember>
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <Group Name="MEASURE_NM3">
                    <GroupExpressions>
                      <GroupExpression>=Fields!MEASURE_NM.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!MEASURE_NM.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>5.43503cm</Size>
                        <CellContents>
                          <Textbox Name="MEASURE_NM3">
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!MEASURE_NM.Value</Value>
                                    <Style>
                                      <FontStyle>Normal</FontStyle>
                                      <FontFamily>Calibri</FontFamily>
                                      <FontSize>9.5pt</FontSize>
                                      <TextDecoration>None</TextDecoration>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Left</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <ToolTip>=IIF(Fields!INVERTED_FLG.Value=TRUE, "Inverse Measure: " + Fields!MEETING_DESC.Value, Fields!MEETING_DESC.Value)</ToolTip>
                            <Style>
                              <Border>
                                <Style>None</Style>
                              </Border>
                              <BottomBorder>
                                <Color>Silver</Color>
                                <Style>Solid</Style>
                              </BottomBorder>
                              <BackgroundColor>Lavender</BackgroundColor>
                              <VerticalAlign>Top</VerticalAlign>
                              <PaddingLeft>6pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>1pt</PaddingTop>
                              <PaddingBottom>1pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <Group Name="LOCATION3">
                            <GroupExpressions>
                              <GroupExpression>=Fields!LOCATION.Value</GroupExpression>
                            </GroupExpressions>
                          </Group>
                          <SortExpressions>
                            <SortExpression>
                              <Value>=Fields!LOCATION.Value</Value>
                            </SortExpression>
                          </SortExpressions>
                          <TablixHeader>
                            <Size>4.63906cm</Size>
                            <CellContents>
                              <Textbox Name="LOCATION3">
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=Fields!LOCATION.Value &amp; " (" &amp; FORMATNUMBER(sum(Fields!TOT_COHORT_CNT.Value),0) &amp; ")"</Value>
                                        <Style>
                                          <FontStyle>Normal</FontStyle>
                                          <FontFamily>Calibri</FontFamily>
                                          <FontSize>9.5pt</FontSize>
                                          <TextDecoration>None</TextDecoration>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style>
                                      <TextAlign>Left</TextAlign>
                                    </Style>
                                  </Paragraph>
                                </Paragraphs>
                                <ActionInfo>
                                  <Actions>
                                    <Action>
                                      <Drillthrough>
                                        <ReportName>Bonus_Quality_Measures</ReportName>
                                        <Parameters>
                                          <Parameter Name="extractDt">
                                            <Value>=Parameters!extractDt.Value</Value>
                                          </Parameter>
                                          <Parameter Name="locationCd">
                                            <Value>=iif(inscope("LOCATION3"),Fields!LOCATION.Value,"")</Value>
                                          </Parameter>
                                          <Parameter Name="providerCd">
                                            <Value>=iif(inscope("PRIMARY_PROVIDER3"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                          </Parameter>
                                          <Parameter Name="drillthroughLocationCd">
                                            <Value>=iif(inscope("LOCATION3"),Fields!LOCATION.Value,"")</Value>
                                          </Parameter>
                                          <Parameter Name="drillthroughProviderCd">
                                            <Value>=iif(inscope("PRIMARY_PROVIDER3"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                          </Parameter>
                                          <Parameter Name="USER_ID">
                                            <Value>=Parameters!USER_ID.Value</Value>
                                          </Parameter>
                                          <Parameter Name="REPORTING_YEAR">
                                            <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                          </Parameter>
                                        </Parameters>
                                      </Drillthrough>
                                    </Action>
                                  </Actions>
                                </ActionInfo>
                                <Style>
                                  <Border>
                                    <Style>None</Style>
                                  </Border>
                                  <BottomBorder>
                                    <Color>Silver</Color>
                                    <Style>Solid</Style>
                                  </BottomBorder>
                                  <BackgroundColor>Lavender</BackgroundColor>
                                  <VerticalAlign>Top</VerticalAlign>
                                  <PaddingLeft>6pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>1pt</PaddingTop>
                                  <PaddingBottom>1pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                          <TablixMembers>
                            <TablixMember>
                              <Group Name="PRIMARY_PROVIDER3">
                                <GroupExpressions>
                                  <GroupExpression>=Fields!PRIMARY_PROVIDER.Value</GroupExpression>
                                </GroupExpressions>
                              </Group>
                              <SortExpressions>
                                <SortExpression>
                                  <Value>=Fields!PRIMARY_PROVIDER.Value</Value>
                                </SortExpression>
                              </SortExpressions>
                              <TablixHeader>
                                <Size>8.47658cm</Size>
                                <CellContents>
                                  <Textbox Name="PRIMARY_PROVIDER3">
                                    <CanGrow>true</CanGrow>
                                    <KeepTogether>true</KeepTogether>
                                    <Paragraphs>
                                      <Paragraph>
                                        <TextRuns>
                                          <TextRun>
                                            <Value>=Fields!PRIMARY_PROVIDER.Value &amp; " (" &amp; FORMATNUMBER(sum(Fields!TOT_COHORT_CNT.Value),0) &amp; ")"</Value>
                                            <Style>
                                              <FontStyle>Normal</FontStyle>
                                              <FontFamily>Calibri</FontFamily>
                                              <FontSize>9.5pt</FontSize>
                                              <TextDecoration>None</TextDecoration>
                                            </Style>
                                          </TextRun>
                                        </TextRuns>
                                        <Style>
                                          <TextAlign>Left</TextAlign>
                                        </Style>
                                      </Paragraph>
                                    </Paragraphs>
                                    <ActionInfo>
                                      <Actions>
                                        <Action>
                                          <Drillthrough>
                                            <ReportName>Bonus_Quality_Measures</ReportName>
                                            <Parameters>
                                              <Parameter Name="extractDt">
                                                <Value>=Parameters!extractDt.Value</Value>
                                              </Parameter>
                                              <Parameter Name="locationCd">
                                                <Value>=iif(inscope("LOCATION3"),Fields!LOCATION.Value,"")</Value>
                                              </Parameter>
                                              <Parameter Name="providerCd">
                                                <Value>=iif(inscope("PRIMARY_PROVIDER3"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                              </Parameter>
                                              <Parameter Name="drillthroughLocationCd">
                                                <Value>=iif(inscope("LOCATION3"),Fields!LOCATION.Value,"")</Value>
                                              </Parameter>
                                              <Parameter Name="drillthroughProviderCd">
                                                <Value>=iif(inscope("PRIMARY_PROVIDER3"),Fields!PRIMARY_PROVIDER.Value,"")</Value>
                                              </Parameter>
                                              <Parameter Name="USER_ID">
                                                <Value>=Parameters!USER_ID.Value</Value>
                                              </Parameter>
                                              <Parameter Name="REPORTING_YEAR">
                                                <Value>=Fields!REPORTING_PERIOD.Value</Value>
                                              </Parameter>
                                            </Parameters>
                                          </Drillthrough>
                                        </Action>
                                      </Actions>
                                    </ActionInfo>
                                    <Style>
                                      <Border>
                                        <Style>None</Style>
                                      </Border>
                                      <BottomBorder>
                                        <Color>Silver</Color>
                                        <Style>Solid</Style>
                                      </BottomBorder>
                                      <BackgroundColor>Lavender</BackgroundColor>
                                      <VerticalAlign>Top</VerticalAlign>
                                      <PaddingLeft>6pt</PaddingLeft>
                                      <PaddingRight>2pt</PaddingRight>
                                      <PaddingTop>1pt</PaddingTop>
                                      <PaddingBottom>1pt</PaddingBottom>
                                    </Style>
                                  </Textbox>
                                </CellContents>
                              </TablixHeader>
                              <Visibility>
                                <Hidden>true</Hidden>
                                <ToggleItem>LOCATION3</ToggleItem>
                              </Visibility>
                            </TablixMember>
                          </TablixMembers>
                          <Visibility>
                            <Hidden>true</Hidden>
                            <ToggleItem>MEASURE_NM3</ToggleItem>
                          </Visibility>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <KeepTogether>true</KeepTogether>
            <DataSetName>DS_BONUS_MEASURES</DataSetName>
            <Top>0.08716cm</Top>
            <Left>0.09cm</Left>
            <Height>1.14229cm</Height>
            <Width>25.89849cm</Width>
            <Style>
              <Border>
                <Color>#0b6c9f</Color>
                <Style>Solid</Style>
                <Width>0.25pt</Width>
              </Border>
              <BackgroundColor>#d4d4e5</BackgroundColor>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>3.15895in</Height>
        <Style />
      </Body>
      <Width>10.23169in</Width>
      <Page>
        <PageHeader>
          <Height>1.03in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Rectangle Name="Rectangle7">
              <ReportItems>
                <Textbox Name="Textbox10">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!SITE_NM.Value, "SITE_INFO")</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <FontSize>11pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <Color>DimGray</Color>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=iif(isnothing(First(Fields!CITY_TXT.Value, "SITE_INFO")) or First(Fields!CITY_TXT.Value, "SITE_INFO")="","", First(Fields!CITY_TXT.Value, "SITE_INFO")) + iif(isnothing(First(Fields!STATE_TXT.Value, "SITE_INFO")) or First(Fields!STATE_TXT.Value, "SITE_INFO")="","", ", " + First(Fields!STATE_TXT.Value, "SITE_INFO"))</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <Color>Gray</Color>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style />
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox10</rd:DefaultName>
                  <Top>0.02795cm</Top>
                  <Left>0.00455cm</Left>
                  <Height>0.97931cm</Height>
                  <Width>9.19396cm</Width>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox13">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!NAME.Value, "DS_TITLE")</Value>
                          <Style>
                            <FontStyle>Normal</FontStyle>
                            <FontFamily>calibri</FontFamily>
                            <FontSize>16pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextDecoration>None</TextDecoration>
                            <Color>#000000</Color>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Center</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox13</rd:DefaultName>
                  <Top>0.80214cm</Top>
                  <Height>0.80434cm</Height>
                  <Width>25.89166cm</Width>
                  <ZIndex>1</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
                <Textbox Name="Textbox47">
                  <CanGrow>true</CanGrow>
                  <KeepTogether>true</KeepTogether>
                  <Paragraphs>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>Epividian® CHORUS</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <FontSize>11pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <Color>DimGray</Color>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value>™</Value>
                          <Style>
                            <FontStyle>Normal</FontStyle>
                            <FontFamily>Calibri</FontFamily>
                            <FontSize>11pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextDecoration>None</TextDecoration>
                            <Color>DimGray</Color>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value> Report</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <FontSize>11pt</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <Color>DimGray</Color>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Right</TextAlign>
                      </Style>
                    </Paragraph>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <FontWeight>Normal</FontWeight>
                            <Format>dd-MMM-yyyy</Format>
                            <Color>Gray</Color>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value> (data)</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <FontWeight>Normal</FontWeight>
                            <Color>Gray</Color>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Right</TextAlign>
                      </Style>
                    </Paragraph>
                    <Paragraph>
                      <TextRuns>
                        <TextRun>
                          <Value>=Now()</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <FontWeight>Normal</FontWeight>
                            <Format>dd-MMM-yyyy</Format>
                            <Color>Gray</Color>
                          </Style>
                        </TextRun>
                        <TextRun>
                          <Value> (run)</Value>
                          <Style>
                            <FontFamily>Calibri</FontFamily>
                            <FontWeight>Normal</FontWeight>
                            <Color>Gray</Color>
                          </Style>
                        </TextRun>
                      </TextRuns>
                      <Style>
                        <TextAlign>Right</TextAlign>
                      </Style>
                    </Paragraph>
                  </Paragraphs>
                  <rd:DefaultName>Textbox46</rd:DefaultName>
                  <Left>8.02674in</Left>
                  <Height>0.38267in</Height>
                  <Width>2.16862in</Width>
                  <ZIndex>2</ZIndex>
                  <Style>
                    <Border>
                      <Style>None</Style>
                    </Border>
                    <PaddingLeft>2pt</PaddingLeft>
                    <PaddingRight>2pt</PaddingRight>
                    <PaddingTop>2pt</PaddingTop>
                    <PaddingBottom>2pt</PaddingBottom>
                  </Style>
                </Textbox>
              </ReportItems>
              <KeepTogether>true</KeepTogether>
              <Top>0.015in</Top>
              <Left>0.03633in</Left>
              <Height>0.63247in</Height>
              <Width>10.19536in</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
              </Style>
            </Rectangle>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageFooter>
          <Height>2.96181in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageFooter>
        <PageHeight>8.5in</PageHeight>
        <PageWidth>11in</PageWidth>
        <LeftMargin>0.3in</LeftMargin>
        <TopMargin>0.3in</TopMargin>
        <BottomMargin>0.3in</BottomMargin>
        <ColumnSpacing>0.05118in</ColumnSpacing>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="extractDt">
      <DataType>DateTime</DataType>
      <DefaultValue>
        <DataSetReference>
          <DataSetName>Extract_Date</DataSetName>
          <ValueField>EXTRACT_DT</ValueField>
        </DataSetReference>
      </DefaultValue>
      <Prompt>extractDt</Prompt>
      <Hidden>true</Hidden>
      <ValidValues>
        <DataSetReference>
          <DataSetName>Extract_Date</DataSetName>
          <ValueField>EXTRACT_DT</ValueField>
          <LabelField>EXTRACT_DT</LabelField>
        </DataSetReference>
      </ValidValues>
    </ReportParameter>
    <ReportParameter Name="locationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>locationCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="providerCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>providerCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughProviderCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughProviderCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="drillthroughLocationCd">
      <DataType>String</DataType>
      <Nullable>true</Nullable>
      <DefaultValue>
        <Values>
          <Value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true" />
        </Values>
      </DefaultValue>
      <AllowBlank>true</AllowBlank>
      <Prompt>drillthroughLocationCd</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="USER_ID">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>6A8C8E25-41B4-4DF1-9FD4-587C0AC9FF2A</Value>
        </Values>
      </DefaultValue>
      <Prompt>USER_ID</Prompt>
    </ReportParameter>
    <ReportParameter Name="REPORTING_YEAR">
      <DataType>String</DataType>
      <DefaultValue>
        <Values>
          <Value>2025</Value>
        </Values>
      </DefaultValue>
      <Prompt>REPORTING_YEAR</Prompt>
      <Hidden>true</Hidden>
    </ReportParameter>
    <ReportParameter Name="COHORT_ID">
      <DataType>Integer</DataType>
      <DefaultValue>
        <Values>
          <Value>1004</Value>
        </Values>
      </DefaultValue>
      <Prompt>COHORT_ID</Prompt>
      <Hidden>true</Hidden>
      <UsedInQuery>True</UsedInQuery>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>5</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>extractDt</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>locationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>providerCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>3</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>drillthroughProviderCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>4</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>drillthroughLocationCd</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>USER_ID</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>REPORTING_YEAR</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>2</ColumnIndex>
          <RowIndex>1</RowIndex>
          <ParameterName>COHORT_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <Variables>
    <Variable Name="MeasureCnt">
      <Value>9</Value>
    </Variable>
  </Variables>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportServerUrl>http://ord-devsql01/ReportServer</rd:ReportServerUrl>
  <rd:ReportID>89b38ba8-54d5-4ac9-bad1-50c5d0e92c91</rd:ReportID>
</Report>