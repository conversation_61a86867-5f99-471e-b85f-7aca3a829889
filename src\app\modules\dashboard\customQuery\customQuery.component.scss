// Add wrapper to constrain overall height
:host {
  display: flex;
  flex-direction: column;
  height: 95vh; // Match other components
  font-family: MuseoSans-300;
  background-color: #eff1f6;
}

.customQueryHeader{
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #d3d5da;
  background-color: transparent;
  flex-shrink: 0; // Don't shrink the header

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    .section-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 2.2rem;
      font-weight: 500;
    }
  }

  .header-center {
    position: absolute;
    left: 61%;
    margin-top: 10px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;

    .customQueryTitle {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 1.2rem;
      font-weight: 400;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: flex-end;
  }
}

.contentWrapper{
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  padding-top: 8px;
  min-height: 0; // Allow shrinking below content size
}

// Left Sidebar Container
.left-sidebar {
  width: 275px;
  display: flex;
  flex-direction: column;
  gap: 0px; // Remove gap so panels touch each other
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0; // Don't shrink the sidebar width
  min-height: 0; // Allow shrinking below content size
  overflow: hidden; // Prevent overflow from sidebar container
}

// Sidebar Panel Base Styles - Wrapper approach for dual border
.sidebar-panel {
  // Outer wrapper for gray border
  border: 1px solid #A9A9A9;
  border-radius: 0; // Default: no border radius for middle panels
  padding: 0px; // Space between border and inner panel
  margin-top: -1px; // Overlap the top border with the panel above
  transition: flex 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // Default: panel takes full available space when open
  flex: 1;
  min-height: 0; // Critical: Allow wrapper to shrink below content size
  display: flex; // Make wrapper a flex container
  flex-direction: column; // Stack content vertically

  // When collapsed, panel takes minimal space (header only)
  &.collapsed {
    flex: 0 0 auto;
  }

  // First panel: curved top corners, straight bottom corners
  &:first-child {
    margin-top: 0;
    border-radius: 16px 16px 0 0; // More rounded corners
  }

  // Last panel: straight top corners, curved bottom corners
  &:last-child {
    border-radius: 0 0 16px 16px; // More rounded corners
  }

  // If there's only one panel, keep all corners curved
  &:first-child:last-child {
    border-radius: 16px; // More rounded corners
  }

  // Inner panel with original styling
  .panel-inner {
    background-color: #fff;
    border: 8px solid #e0e0e0;
    border-radius: inherit; // Inherit border radius from wrapper
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1; // Take all available space in wrapper
    min-height: 0; // Allow flex child to shrink below content size
  }

  // Remove inner border where panels connect
  &:not(:first-child) .panel-inner {
    border-top: none; // Remove top border on panels that aren't first
  }

  &:not(:last-child) .panel-inner {
    border-bottom: none; // Remove bottom border on panels that aren't last
  }

  // Panel header now inside the inner panel
  .panel-inner .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;

    &:hover {
      background-color: #f8f9fa;
    }

    h3 {
      margin: 0;
      font-family: Museo500-Regular;
      font-size: 1.3rem;
      font-weight: 500;
      color: #0071BC;
    }

    .panel-toggle-btn {
      margin-right: -20px;
      color: #666;
      background-color: transparent !important;

      &:hover {
        background-color: transparent !important;
      }

      // Override the specific MDC classes that cause the circular hover effect
      .mat-mdc-button-persistent-ripple,
      .mdc-icon-button__ripple {
        background-color: transparent !important;
        display: none !important;
        opacity: 0 !important;
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}

// Global override for the specific MDC classes that cause the circular hover effect
::ng-deep .panel-header .panel-toggle-btn {
  .mat-mdc-button-persistent-ripple,
  .mdc-icon-button__ripple {
    background-color: transparent !important;
    display: none !important;
    opacity: 0 !important;
  }
}

.sidebar-panel {
  .panel-inner .panel-content {
    flex: 1;
    overflow: hidden;
    min-height: 0; // Allow flex child to shrink below content size

    &.hidden {
      display: none;
    }
  }
}

// Reports Panel Specific Styles
.reports-panel {
  // Reports panel takes all available space
  flex: 1;

  .panel-inner .panel-content {
    overflow-y: auto;
    min-height: 0; // Allow flex child to shrink below content size

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Main content area
.main-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.templateContainer{
  padding-left: 20px;
}

.mat-header-cell {
  color: #FFFFFF;
  background-color: #005391;
  border-right: 2px groove whitesmoke;
  text-align: center !important;
}

.mat-row:nth-child(2n+1)
{
  background-color: #D4D4E5;
}

.mat-row:not(:nth-child(2n+1))
{
  background-color: #FFFFFF;
}

.mat-cell {
  border-right: 2px groove whitesmoke;
}


.EpividianDataTable {
  display: flex;
  position: relative;
  max-height: 89vh;
  overflow-x: auto;
  flex-direction: column;
  margin-top: 10px;
  margin-left: 10px;
}

.mat-table {
  overflow: auto;
  max-height: 500px;
}

 .mat-column-name{
border-right: 5px solid grey;
align-self: stretch;
text-align: center


}
.mat-column-position{
border-right: 5px solid grey;
align-self: stretch;
text-align: center;

}
.mat-column-weight{
border-right: 5px solid grey;
align-self: stretch;
text-align: center;

}
.mat-column-symbol{

text-align: center;
align-self: stretch;
}
.mat-column-weight{
   align-self: stretch;
}

.patientColumn{
  cursor: pointer;
}

.noWrap{
  text-wrap: nowrap;
}
