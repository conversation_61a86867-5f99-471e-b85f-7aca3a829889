import { Component, OnInit, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { blankReportParamData, IReportParamData } from 'src/app/shared-services/ep-api-handler/models/report-param-data-model';
import { PanelService } from '../PanelService';
import { btnColor, ReportPanelTypes } from '../report-panel-enums';
import { LayoutService } from 'src/app/modules/shared-modules/layouts/services/layout/layout.service';

@Component({
  selector: 'pnl-hcv-location',
  templateUrl: './pnl-hcv-location.component.html',
  styleUrl: './pnl-hcv-location.component.scss'
})
export class PnlHcvLocationComponent implements OnInit {

  rptbtnColor: string;
  @Input() rptpanLocation: FormControl;
  @Input() locationSearch: FormControl;

  locationData: IReportParamData[] =[] as IReportParamData[];
  filteredLocations: IReportParamData = {} as IReportParamData;
  isRunButtonEnable: boolean = false;
  noData: boolean = false;
  lblWarningMsg: string = 'To run your report, please make a selection for location. Note: only locations with patients who have HCV risk score data are listed.';
  noDataMsg: string = "There are no locations availible";

  constructor(public panelService: PanelService, private layoutService: LayoutService) {
    this.rptpanLocation = new FormControl;
    this.locationSearch = new FormControl;
    this.rptbtnColor= btnColor.btnSecondColor;
  }

  ngOnInit(): void {
    // Show spinner before API call
    this.layoutService.showSpinner();

    this.panelService.GetPanelData(ReportPanelTypes.pnlHcvLocation).subscribe({
      next: (response) => {
        this.setLocationDropDown(response);

        // Hide spinner on success
        this.layoutService.hideSpinner();
      },
      error: (err) => {
        console.error("Error fetching panel data:", err);

        // Hide spinner on error
        this.layoutService.hideSpinner();
      }
    });
  }

  setLocationDropDown(response: IReportParamData[]){
    const locationControlId = 0;
    let wasDefaultSet = false
    let hasData = this.panelService.HasReportParamData(response, locationControlId);

    /* ======================== ADD [Unassigned Location] If not present ========================= */
    /* ===== We will modify the API to return, but there could also be an error server side ====== */
    /* =========================================================================================== */
    // If no data is present then initalize as empty.
    // Clear out the data added by the blank initalizer.
    if (!hasData){
      response = [{ ...blankReportParamData }];
      response[locationControlId].data = [];
    }

    // Determine if the API returned -1 / Unassigned Location.
    const hasUnassigned = response[locationControlId]?.data.some(d => d.key === -1) ?? false;

    // If not present add it now.
    if (!hasUnassigned) {
      response[locationControlId].data.push({
        groupId: 0,
        key: -1,
        value: "Unassigned Location",
        subtext: ''
      });
    }
    /* ======================== ADD [Unassigned Location] If not present ========================= */

    // At minimum we should [-1 / Unassigned Location] present.
    this.locationData = response;

    // If we have data determine if there is a default.
    // Else display the no location data message.
    if (hasData) {
      // If response is not null, has at least one item, and response[0].data has rows.
      wasDefaultSet = this.getDefaults();

      // 29475 - Hide
      //} else {
      // Notify user there is no location data.
      // this.noData = true;
    }

    // This assigns the data to the drop down. "All" means not filtered!
    this.comboFilter("All");

    if (!wasDefaultSet){
      // Ensure we set the default selection after the view is initialized
      this.setDefaultSelection();
    }
  }

  setDefaultSelection() {
    // Check if filteredLocations has data
    const firstLocation = this.filteredLocations?.data?.[0];

    if (firstLocation) {
      // If there's data, set the first item as the default
      this.locationSearch.setValue(firstLocation);
      this.rptpanLocation.setValue(firstLocation.key);
    }

    this.setButtonColor();
  }

  onSelectionChange(event): void {
    this.rptpanLocation.setValue(event.option.value.key);
    this.setButtonColor();
  }

  getDefaults(): boolean {
    let isDefaultSet = false; // Variable to hold the boolean value

    if (this.locationData && this.locationData[0]?.data) {
      const defaultLocation = this.locationData[0].data.find(item => item.value === this.locationData[0].default);

      // If defaultLocation is found, set the form control values and mark the default as set
      if (defaultLocation) {
        this.locationSearch.setValue(defaultLocation);
        this.rptpanLocation.setValue(defaultLocation.key);
        isDefaultSet = true; // Set to true if a default location was found
      }
    }

    this.setButtonColor();
    return isDefaultSet; // Return the boolean indicating if default was set
  }


  displayFn(option):any {
    if (option)
    {
      return option.value ? option.value : option.key;
    }
      return '';
  }

  comboFilter(searchString :string)
  {
    this.filteredLocations = this.panelService.ComboAutoComplete(searchString, 0, this.locationData, false)[0];
  }

  setButtonColor(): void {
    if (!this.locationSearch.value || !this.locationSearch.value.value) {
      this.rptbtnColor = btnColor.btnSecondColor;
      this.isRunButtonEnable = false;
    } else {
      this.rptbtnColor = btnColor.btnPrimaryColor;
      this.isRunButtonEnable = true;
    }
  }
}
