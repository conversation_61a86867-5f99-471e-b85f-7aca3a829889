<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns:am="http://schemas.microsoft.com/sqlserver/reporting/authoringmetadata">
  <am:AuthoringMetadata>
    <am:CreatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.14.36401.2</am:Version>
    </am:CreatedBy>
    <am:UpdatedBy>
      <am:Name>SSDTRS</am:Name>
      <am:Version>17.14.36401.2</am:Version>
    </am:UpdatedBy>
    <am:LastModifiedTimestamp>2025-08-15T14:07:24.5142718Z</am:LastModifiedTimestamp>
  </am:AuthoringMetadata>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="dsSite">
      <DataSourceReference>dsSite</DataSourceReference>
      <rd:SecurityType>None</rd:SecurityType>
      <rd:DataSourceID>b9efb540-e32f-45de-911a-086da92e7243</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="HCVPatients">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <QueryParameters>
          <QueryParameter Name="@LOCATION_ID">
            <Value>=Parameters!LOCATION_ID.Value</Value>
          </QueryParameter>
        </QueryParameters>
        <CommandText>/*
-- Author		:	Jack Averill
-- Created Date	:	02/27/2025
--
-- Project		:	Gilead Relink C Study
-- Description	:	This is code for an SSRS dataset to identify the patients qualifying for the
--                  HCV screening alert for a measure (MEASURE_CD = 'CC010') involving ZS risk scores.
--                  The SQL first collects patients qualifying for the alerting measure and extracts the
--                  ZS Score. Next, it incorporates the next scheduled visit date and appointment type (if 
--                  available) for each patient collected in the first step before displaying the full collected
--                  output. The report will receive a parameter for a single clinic (@LOCATION_ID) that will affect a 
--                  filter so that providers will view patients for the clinic selected; this parameter filter
--                  will be based on the CLEAN.DEMOGRAPHICS.LOCATION_ID (to filter on the location/clinic of 
--                  qualifying patients). 
--
-- Updates		:	
-- 03/01/2025 - Performance tuning and output formatting changes:
--              • Moved LOCATION_ID filter earlier in the query to reduce rowset size before processing.
--              • Replaced ROW_NUMBER() approach for next scheduled visit with OUTER APPLY + TOP(1) for faster lookups.
--              • Refactored ZS Score parsing into a CROSS APPLY, parsed once, and converted to numeric for sorting.
--              • Cleaned ORDER BY to use native types (date, numeric) to avoid non-sargable casts and improve sort performance.
--              • Ensured ZS_FEATURES (ACTION_TXT) is carried through to the final output.
--              • Updated ZS_SCORE formatting to always display two decimal places followed by a percent sign.
*/

-- SSRS typically supplies @LOCATION_ID as a parameter.
-- If testing locally, uncomment and set a value:
-- DECLARE @LOCATION_ID int = 123;  

-- Anchor "index date" for comparing future schedules
DECLARE @INDEX_DT date = CAST(GETDATE() AS date);

-- CTE: Pull qualifying measure-detail rows for the latest rolling week/period
-- Joins to MEASURE with matching MEASURE_ID/REPORTING_PERIOD/ROLLING_WEEK.
-- EXCLUDE_FLG = 0 limits to included rows.
WITH Base AS (
    SELECT md.DEMOGRAPHICS_ID, md.DETAIL_TXT, md.ACTION_TXT
    FROM REPORT.MEASURES_DTL md
    JOIN REPORT.MEASURE m
      ON m.MEASURE_ID = md.MEASURE_ID
     AND m.REPORTING_PERIOD = md.REPORTING_PERIOD
     AND m.LATEST_ROLLING_WEEK = md.ROLLING_WEEK
    WHERE m.MEASURE_ID = 124
      AND md.EXCLUDE_FLG = 0
),

-- CTE: Early clinic filter
-- We shrink the working set ASAP by joining DEMOGRAPHICS here and filtering on LOCATION_ID.
-- This reduces the workload for later parsing and schedule lookups.
BaseAtClinic AS (
    SELECT b.DEMOGRAPHICS_ID,
           b.DETAIL_TXT,
           b.ACTION_TXT,
           cd.PATIENT_SEARCH_DISPLAY,
           cd.GENDER_DESC
    FROM Base b
    JOIN CLEAN.DEMOGRAPHICS cd
      ON cd.DEMOGRAPHICS_ID = b.DEMOGRAPHICS_ID
    WHERE cd.LOCATION_ID = @LOCATION_ID
),

-- CTE: Parse ZS score once, keep both text and numeric forms
-- We use CROSS APPLY to do the string extraction once per row.
-- The numeric version (ZS_SCORE_NUM) enables fast, correct numeric sorting later.
Parsed AS (
    SELECT
        bac.DEMOGRAPHICS_ID,
        bac.PATIENT_SEARCH_DISPLAY,
        bac.GENDER_DESC,
        bac.ACTION_TXT,  -- retained as ZS_FEATURES downstream
        zs.ZS_SCORE_TXT, -- original token near '%', e.g. ' 65%'
        TRY_CONVERT(decimal(6,3), LTRIM(REPLACE(zs.ZS_SCORE_TXT, '%', ''))) AS ZS_SCORE_NUM
    FROM BaseAtClinic bac
    CROSS APPLY (
        SELECT
          -- Extract the token immediately before the '%' in DETAIL_TXT.
          -- Steps:
          --   1) SUBSTRING up to the first '%' (left part).
          --   2) REVERSE that substring so the '%' side is at the left.
          --   3) Take characters up to the first space (this yields e.g. ' %56' reversed).
          --   4) REVERSE back to original order, yielding something like ' 56%'.
          CASE
            WHEN CHARINDEX('%', bac.DETAIL_TXT) &gt; 0 THEN
              REVERSE(SUBSTRING(
                  REVERSE(SUBSTRING(bac.DETAIL_TXT, 1, CHARINDEX('%', bac.DETAIL_TXT))),
                  1,
                  NULLIF(CHARINDEX(' ',
                        REVERSE(SUBSTRING(bac.DETAIL_TXT, 1, CHARINDEX('%', bac.DETAIL_TXT)))
                  ), 0)
              ))
            ELSE NULL
          END
    ) zs (ZS_SCORE_TXT)
),

-- CTE: Attach the next scheduled visit (if any) with OUTER APPLY + TOP(1)
-- This pattern avoids window/sort overhead from ROW_NUMBER().
-- With proper indexes on CLEAN.SCHEDULE, the engine can do a narrow ordered seek.
Final AS (
    SELECT p.DEMOGRAPHICS_ID,
           p.PATIENT_SEARCH_DISPLAY,
           p.GENDER_DESC,
           p.ZS_SCORE_TXT,
           p.ZS_SCORE_NUM,
           p.ACTION_TXT AS ZS_FEATURES,
           oa.NEXT_SCHED_VIS_DT,
           oa.NEXT_SCHED_APPT_TYPE
    FROM Parsed p
    OUTER APPLY (
        SELECT TOP (1)
               s.SCHEDULE_DT      AS NEXT_SCHED_VIS_DT,
               s.APPOINTMENT_TYPE AS NEXT_SCHED_APPT_TYPE
        FROM CLEAN.SCHEDULE s
        WHERE s.DEMOGRAPHICS_ID = p.DEMOGRAPHICS_ID
          AND s.VALID_FLG = 1
          AND s.STATUS_CD = '0'
          AND s.SCHEDULE_DT &gt;= @INDEX_DT
        ORDER BY s.SCHEDULE_DT, s.SCHEDULE_ID
    ) oa
)

-- Final projection for SSRS
-- ZS_SCORE formatted with two decimal places; BACKGROUND_COLOR derived from days until next visit.
SELECT
    f.PATIENT_SEARCH_DISPLAY,
    f.GENDER_DESC,
    CASE
        WHEN f.ZS_SCORE_NUM IS NOT NULL
            THEN FORMAT(f.ZS_SCORE_NUM, 'N2') + '%'  -- two decimal places (e.g., 65.00%)
            -- NOTE: FORMAT is easy to read but slower on very large rowsets.
            -- If needed, replace with: RTRIM(CONVERT(varchar(20), ROUND(f.ZS_SCORE_NUM, 2))) + '%'
        ELSE f.ZS_SCORE_TXT                     -- fall back to original token if parsing failed
    END AS ZS_SCORE,
    f.ZS_FEATURES,                              -- sourced from ACTION_TXT
    f.NEXT_SCHED_VIS_DT,
    f.NEXT_SCHED_APPT_TYPE,
    CASE                                         -- color bands by days until visit (relative to @INDEX_DT)
        WHEN f.NEXT_SCHED_VIS_DT IS NULL THEN 'Red'
        WHEN DATEDIFF(DAY, @INDEX_DT, f.NEXT_SCHED_VIS_DT) &gt; 180 THEN 'Orange'
        WHEN DATEDIFF(DAY, @INDEX_DT, f.NEXT_SCHED_VIS_DT) BETWEEN 91 AND 180 THEN 'Yellow'
        ELSE NULL
    END AS BACKGROUND_COLOR
FROM Final f
-- ORDER BY uses native types only (date, numeric) for cleaner, faster sorts.
ORDER BY
    CASE WHEN f.NEXT_SCHED_VIS_DT IS NULL THEN 0 ELSE 1 END,  -- show "no next visit" first
    f.NEXT_SCHED_VIS_DT DESC,                                  -- then farthest future first
    f.ZS_SCORE_NUM DESC;                                       -- tie-breaker by higher ZS score
</CommandText>
      </Query>
      <Fields>
        <Field Name="GENDER_DESC">
          <DataField>GENDER_DESC</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ZS_SCORE">
          <DataField>ZS_SCORE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NEXT_SCHED_VIS_DT">
          <DataField>NEXT_SCHED_VIS_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="ZS_FEATURES">
          <DataField>ZS_FEATURES</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="NEXT_SCHED_APPT_TYPE">
          <DataField>NEXT_SCHED_APPT_TYPE</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="PATIENT_SEARCH_DISPLAY">
          <DataField>PATIENT_SEARCH_DISPLAY</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BACKGROUND_COLOR">
          <DataField>BACKGROUND_COLOR</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="Extract_Date">
      <Query>
        <DataSourceName>dsSite</DataSourceName>
        <CommandText>SELECT        EXTRACT_DT
FROM            CLEAN.SITE</CommandText>
      </Query>
      <Fields>
        <Field Name="EXTRACT_DT">
          <DataField>EXTRACT_DT</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <Tablix Name="Tablix1">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>5.18057in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.20903in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.38542in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Patient</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox1</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Sex</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox2</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox4">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>HCV Risk Score</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox4</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Factors Impacting HCV Risk Score</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox6</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Date of next scheduled visit (as of last data refresh)</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>Type of next scheduled visit</Value>
                                  <Style>
                                    <FontFamily>Calibri</FontFamily>
                                    <FontSize>12pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox10</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#0b6c9f</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="PATIENT_SEARCH_DISPLAY">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!PATIENT_SEARCH_DISPLAY.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>PATIENT_SEARCH_DISPLAY</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="GENDER_DESC">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!GENDER_DESC.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>GENDER_DESC</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ZS_SCORE">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ZS_SCORE.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ZS_SCORE</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ZS_FEATURES">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ZS_FEATURES.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ZS_FEATURES</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="NEXT_SCHED_VIS_DT">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=IIF(IsNothing(Fields!NEXT_SCHED_VIS_DT.Value),Nothing,FormatDateTime(Fields!NEXT_SCHED_VIS_DT.Value,Dateformat.ShortDate))</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>NEXT_SCHED_VIS_DT</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(IsNothing(Fields!BACKGROUND_COLOR.Value), Nothing, Fields!BACKGROUND_COLOR.Value)</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="NEXT_SCHED_APPT_TYPE">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!NEXT_SCHED_APPT_TYPE.Value</Value>
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>NEXT_SCHED_APPT_TYPE</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>LightGrey</Color>
                              <Style>Solid</Style>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>HCVPatients</DataSetName>
            <Top>0.06944in</Top>
            <Height>0.63542in</Height>
            <Width>12.4896in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>0.78125in</Height>
        <Style />
      </Body>
      <Width>12.4896in</Width>
      <Page>
        <PageHeader>
          <Height>0.77083in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="Textbox15">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>HCV Risk Scores</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>calibri</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>#000000</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox13</rd:DefaultName>
              <Top>0.33866cm</Top>
              <Left>10.56881cm</Left>
              <Height>0.80434cm</Height>
              <Width>10.74468cm</Width>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
            <Textbox Name="Textbox47">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>Epividian® CHORUS</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>™</Value>
                      <Style>
                        <FontStyle>Normal</FontStyle>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <TextDecoration>None</TextDecoration>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> Report</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontSize>11pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>DimGray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!EXTRACT_DT.Value, "Extract_Date")</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (data)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=Now()</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Format>dd-MMM-yyyy</Format>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> (run)</Value>
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value />
                      <Style>
                        <FontFamily>Calibri</FontFamily>
                        <FontWeight>Normal</FontWeight>
                        <Color>Gray</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>Textbox46</rd:DefaultName>
              <Top>0.33866cm</Top>
              <Left>10.28402in</Left>
              <Height>0.58333in</Height>
              <Width>2.20558in</Width>
              <ZIndex>1</ZIndex>
              <Style>
                <Border>
                  <Style>None</Style>
                </Border>
                <PaddingLeft>2pt</PaddingLeft>
                <PaddingRight>2pt</PaddingRight>
                <PaddingTop>2pt</PaddingTop>
                <PaddingBottom>2pt</PaddingBottom>
              </Style>
            </Textbox>
          </ReportItems>
          <Style>
            <Border>
              <Style>None</Style>
            </Border>
          </Style>
        </PageHeader>
        <PageWidth>16in</PageWidth>
        <InteractiveHeight>0in</InteractiveHeight>
        <InteractiveWidth>0in</InteractiveWidth>
        <LeftMargin>1in</LeftMargin>
        <RightMargin>1in</RightMargin>
        <TopMargin>1in</TopMargin>
        <BottomMargin>1in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="LOCATION_ID">
      <DataType>Integer</DataType>
      <DefaultValue>
        <Values>
          <Value>-1</Value>
        </Values>
      </DefaultValue>
      <Prompt>LOCATION_ID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>4</NumberOfColumns>
      <NumberOfRows>2</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>LOCATION_ID</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportServerUrl>http://ord-devsql01/ReportServer</rd:ReportServerUrl>
  <rd:ReportID>2af2644c-78b6-424a-9fe3-73c9aa7d922e</rd:ReportID>
</Report>
