<div>
  <mat-sidenav-container class="dashboardWrapper">
    <mat-sidenav class="dashboardSideNav" role="navigation" mode="side" [opened]="true">
      <div class="expandedSideNav" *ngIf="layoutService.getExpandSideNav(); else collapsed">
        <div class="logoContainer"></div>
        <div class="sidenavSubContainer">
          <div class="dashboardTabs" hideSingleSelectionIndicator>
            <div class="dashboardOption" routerLink="/Dashboard" [routerLinkActive]="['isActive']" [routerLinkActiveOptions]="{ exact: true }">
              <div class="dashboardTabItem uncollapsedItem">
                <div class="itemIcon">
                  <img src="../../../../../assets/images/Patient-Preview.svg" alt="Patient Preview">
                </div>
                Patient Preview
              </div>
            </div>
            <div class="dashboardOption" routerLink="/Dashboard/huddle" [routerLinkActive]="['isActive']" [routerLinkActiveOptions]="{ exact: true }">
              <div class="dashboardTabItem uncollapsedItem">
                <div class="itemIcon">
                  <img id="huddleIcon" src="../../../../../assets/images/CHORUS_Huddle_icon.svg">
                </div>
                Huddle
              </div>
            </div>
            <div class="dashboardOption" routerLink="/OutreachAndRetention" routerLinkActive="isActive"  [hidden]="!hasOutreach">
              <div class="dashboardTabItem uncollapsedItem">
                <div class="itemIcon">
                  <img src="../../../../../assets/images/Outreach icon.svg" alt="Outreach &amp; Retention">
                </div>
                Outreach &amp; Retention
              </div>
            </div>
            <div class="dashboardOption" [routerLink]="rootSharePath" routerLinkActive="isActive">
              <div class="dashboardTabItem uncollapsedItem">
                <div class="itemIcon">
                  <img src="../../../../../assets/images/Document icon.svg" >
                </div>
                Documents
              </div>
            </div>
            <div class="dashboardOption" (click)="navigateToDefaultReport()" routerLink="/Dashboard/Report" [routerLinkActive]="['isActive']" #reportTab="routerLinkActive">
              <div class="dashboardTabItem uncollapsedItem">
                <div class="itemIcon">
                  <img src="../../../../../assets/images/Reports icon.svg" alt="Reports">
                </div>
                Reports
              </div>
              <div routerLink="/Dashboard/Report" hidden="true"></div>
            </div>
            <!-- Bonus Measures menu item -->
            <div class="dashboardOption bonus-measures-menu" [routerLink]="'/Dashboard/BonusMeasures/' + siteId" [routerLinkActive]="['isActive']" *ngIf="hasBonusMeasuresAccess">
              <div class="dashboardTabItem uncollapsedItem">
                <div class="itemIcon">
                  <mat-icon class="bonus-measures-icon">star</mat-icon>
                </div>
                Bonus Measures
              </div>
            </div>
            <!-- Separator line before Admin -->
            <div class="menu-separator" *ngIf="hasAdminAccess"></div>
            <!-- Admin menu item -->
            <div class="dashboardOption" routerLink="/Admin" routerLinkActive="isActive" *ngIf="hasAdminAccess">
              <div class="dashboardTabItem uncollapsedItem">
                <div class="itemIcon">
                  <mat-icon>admin_panel_settings</mat-icon>
                </div>
                Administration
              </div>
            </div>

            <!-- Debug info - remove in production -->
            <div style="display: none;">Admin access: {{hasAdminAccess}}</div>
          </div>

          <div class="userSection">
            <div class="userInfoContainer">
              <button class="userButton" [matMenuTriggerFor]="menu">
                <div class="avatar">
                  <div class="avatarIcon">
                    <div>{{getFormattedInitials()}}</div>
                  </div>
                </div>
              </button>
              <div class="userInfo">{{firstName}} {{lastName}}</div>
              <div class="userInfo userEmail">{{email}}</div>
              <div class="siteName">{{currentSiteName}}</div>
              <!-- Sign Out button below site name -->
              <button class="signOutButton" (click)="signOut()">
                <mat-icon>logout</mat-icon>
                <span>Sign Out</span>
              </button>
            </div>
            <div class="bottomInfoContainer">
              <div class="dateLastLoaded">
                <div class="updateIcon"></div>
                <div class="dateText">{{lblDateLastUpdate}}</div>
              </div>
              <div class="chorusVersion">
                Epividan Chorus 29.1
              </div>
            </div>
          </div>
        </div>
        <div class="collapseIcon">
          <button class="collapseButtonExpanded" (click)="layoutService.toggleExpandSideNav()"><mat-icon>keyboard_double_arrow_left</mat-icon></button>
        </div>
      </div>

      <ng-template #collapsed>
        <div class="collapsedSideNav">
          <div class="logoContainerCollapsed"></div>
          <div class="sidenavSubContainer">
            <div class="dashboardTabs" hideSingleSelectionIndicator>
              <div class="dashboardOption" routerLink="/Dashboard" [routerLinkActive]="['isActive']" [routerLinkActiveOptions]="{ exact: true }">
                <div class="dashboardTabItem collapsedItem">
                  <div class="itemIcon collapsedIcon">
                    <img src="../../../../../assets/images/Patient-Preview.svg">
                  </div>
                  <div class="wrapText">Patient Preview</div>
                </div>
              </div>
              <div class="dashboardOption" routerLink="/Dashboard/huddle" [routerLinkActive]="['isActive']">
                <div class="dashboardTabItem collapsedItem">
                  <div class="itemIcon collapsedIcon">
                    <img id="huddleIcon" src="../../../../../assets/images/CHORUS_Huddle_icon.svg">
                  </div>
                  Huddle
                  </div>
              </div>
              <div class="dashboardOption" routerLink="/OutreachAndRetention" [routerLinkActive]="['isActive']" [hidden]="!hasOutreach">
                <div class="dashboardTabItem collapsedItem">
                  <div class="itemIcon collapsedIcon">
                    <img src="../../../../../assets/images/Outreach icon.svg">
                  </div>
                  <div class="wrapText">Outreach & Retention </div>
                </div>
              </div>
              <div class="dashboardOption" [routerLink]="rootSharePath" [routerLinkActive]="['isActive']">
                <div class="dashboardTabItem collapsedItem">
                  <div class="itemIcon collapsedIcon">
                    <img src="../../../../../assets/images/Document icon.svg" >
                  </div>
                  Documents
                </div>
              </div>
              <div class="dashboardOption" (click)="navigateToDefaultReport()" routerLink="/Dashboard/Report" [routerLinkActive]="['isActive']" #reportTab="routerLinkActive">
                <div class="dashboardTabItem collapsedItem">
                  <div class="itemIcon collapsedIcon">
                    <img src="../../../../../assets/images/Reports icon.svg">
                  </div>
                  Reports
                </div>
                <div routerLink="/Dashboard/Report" hidden="true"></div>
              </div>
              <!-- Bonus Measures menu item -->
              <div class="dashboardOption" [routerLink]="'/Dashboard/BonusMeasures/' + siteId" *ngIf="hasBonusMeasuresAccess">
                <div class="dashboardTabItem collapsedItem">
                  <div class="itemIcon collapsedIcon">
                    <mat-icon>star</mat-icon>
                  </div>
                  Bonus
                </div>
              </div>
              <!-- Separator line before Admin -->
              <div class="menu-separator" *ngIf="hasAdminAccess"></div>
              <div class="dashboardOption" routerLink="/Admin" [routerLinkActive]="['isActive']" *ngIf="hasAdminAccess">
                <div class="dashboardTabItem collapsedItem">
                  <div class="itemIcon collapsedIcon">
                    <mat-icon>admin_panel_settings</mat-icon>
                  </div>
                  Admin
                </div>
              </div>
            </div>
            <div class="userSection collapsedUserSection">
              <div class="userInfo">
                <button mat-button [matMenuTriggerFor]="menu" class="avatar">
                  <div class="avatarIcon">
                    <div>{{getFormattedInitials()}}</div>
                  </div>
                </button>
              </div>
            </div>
          </div>
          <div class="collapseIcon">
            <button class="collapseButtonCollapsed" (click)="layoutService.toggleExpandSideNav()"><mat-icon>keyboard_double_arrow_right</mat-icon></button>
          </div>
        </div>
      </ng-template>
    </mat-sidenav>

    <mat-menu #menu="matMenu" [overlapTrigger]="false" yPosition="above" xPosition="before" class="user-dropdown-menu" [hasBackdrop]="true">
      <button mat-menu-item (click)=openChangePasswordDialog()>
        <mat-icon>lock</mat-icon>
        <span>Change Password</span>
      </button>
      <button mat-menu-item (click)=openMobileDeviceDialog()>
        <mat-icon>settings</mat-icon>
        <span>Device Management</span>
      </button>
      <button *ngIf="showSupportLink" mat-menu-item (click)=openSupportDetails()>
        <mat-icon>info</mat-icon>
        <span>Support Information</span>
      </button>
      <button mat-menu-item [matMenuTriggerFor]="Sitemenu">
        <mat-icon>business</mat-icon>
        <span>Sites</span>
      </button>
      <!-- Show logout menu item only when navigation is collapsed -->
      <button *ngIf="!layoutService.getExpandSideNav()" mat-menu-item (click)="signOut()">
        <mat-icon>logout</mat-icon>
        <span>Sign Out</span>
      </button>
    </mat-menu>
    <mat-menu #Sitemenu="matMenu">
      <button mat-menu-item (click)="SetCurrentSite(site)"  *ngFor='let site of siteList'>
        <span>{{site.name}}</span>
      </button>
    </mat-menu>

    <mat-sidenav-content [ngClass]="{'contentWrapperExpanded': true, 'contentWrapperCollapsed': !layoutService.getExpandSideNav()}">
      <div class="contentLayout">
        <app-header></app-header>
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
  </div>
