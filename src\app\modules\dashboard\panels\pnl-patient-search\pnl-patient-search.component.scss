.searchCriteriaDiv {
    position: relative;
    margin: 2px;
    padding: 15px 0px;
    width: 100%;
    z-index: 1;
    background-color: white;
}

// Label and Dropdown Row
.label-dropdown-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 15px;
}

.label-container {
    flex-shrink: 0;
    //min-width: fit-content;
}

.field-label {
    color: black;
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
}

.dropdown-container {
    flex: 1;
    min-width: 0; // Allow flex item to shrink
    max-width: 105px; // Reasonable maximum width
    margin-left: -10px;
}

.dropdown-container select {
    width: 100%;
    min-width: 105px; // Minimum width for usability
    max-width: 100%;
    border: 1px solid #d3d3d3;
    border-radius: 4px;
    margin-right: 20px;
   // padding: 6px;
    background-color: white;
    font-size: 14px;
    line-height: 1.4;

    // Improve appearance and usability
    &:focus {
        outline: none;
        border-color: #0071bc;
        box-shadow: 0 0 0 2px rgba(0, 113, 188, 0.1);
    }

    &:hover {
        border-color: #999;
    }

    // Ensure text doesn't overflow
    option {
        padding: 4px 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

// Patient Search Input Row
.input-row {
    margin-bottom: 15px;
}

.patient-search-input {
    width: 100%;
    max-width: 100%;
}

// Run Button Row
.button-row {
    margin-top: 15px;
    padding: 0 16px;
}

.run-button {
    width: 100% !important;
    padding: 8px 16px !important;
    box-sizing: border-box;
    border: none;
    cursor: pointer;
    font-size: 14px;
    height: auto;
    min-height: 35px;
}

// Legacy styles for backward compatibility
.elementDiv {
    padding-right: 15px;
    display: table-cell;
    color: #0071bc;
    float: left;
    font-weight: 500;
}

.btnWidth {
    border: medium none;
    color: #FFFFFF;
    cursor: pointer;
    float: left;
    font-size: 14px;
    height: 30px;
    padding: 0 10px;
    text-align: center;
    width: 150px;
}

// Responsive adjustments for smaller screens
@media (max-width: 768px) {
    .label-dropdown-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .dropdown-container {
        max-width: 100%;
        width: 100%;
    }

    .dropdown-container select {
        min-width: 100%;
    }
}

// Ensure proper spacing on very small screens
@media (max-width: 480px) {
    .searchCriteriaDiv {
        padding: 12px;
        margin: 1px;
    }

    .label-dropdown-row {
        gap: 6px;
    }
}
