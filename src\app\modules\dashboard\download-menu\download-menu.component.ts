import { Component, EventEmitter, Output, HostListener } from '@angular/core';

@Component({
  selector: 'app-download-menu',
  templateUrl: './download-menu.component.html',
  styleUrls: ['./download-menu.component.css']
})
export class DownloadMenuComponent {
  // Event emitters for download actions
  @Output() printClicked = new EventEmitter<void>();
  @Output() pdfExportClicked = new EventEmitter<void>();
  @Output() excelExportClicked = new EventEmitter<void>();
  @Output() csvExportClicked = new EventEmitter<void>();

  // Dropdown state
  isOpen = false;

  constructor() { }

  // Toggle dropdown
  toggleDropdown(): void {
    this.isOpen = !this.isOpen;
  }

  // Close dropdown
  closeDropdown(): void {
    this.isOpen = false;
  }

  // Handle clicks outside the dropdown
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown')) {
      this.isOpen = false;
    }
  }

  // Event handler methods
  onPrintClick(): void {
    this.printClicked.emit();
    this.closeDropdown();
  }

  onPdfExportClick(): void {
    this.pdfExportClicked.emit();
    this.closeDropdown();
  }

  onExcelExportClick(): void {
    this.excelExportClicked.emit();
    this.closeDropdown();
  }

  onCsvExportClick(): void {
    this.csvExportClicked.emit();
    this.closeDropdown();
  }
}
